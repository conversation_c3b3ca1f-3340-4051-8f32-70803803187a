@font-face {
	font-family: 'ult-vid-cntrl';
	src:url('ult-vid-cntrl.eot?2kpffx');
	src:url('ult-vid-cntrl.eot?#iefix2kpffx') format('embedded-opentype'),
		url('ult-vid-cntrl.woff?2kpffx') format('woff'),
		url('ult-vid-cntrl.ttf?2kpffx') format('truetype'),
		url('ult-vid-cntrl.svg?2kpffx#ult-vid-cntrl') format('svg');
	font-weight: normal;
	font-style: normal;
}

[class^="ult-vid-cntrl"], [class*=" ult-vid-cntrl"] {
	font-family: 'ult-vid-cntrl';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.ult-vid-cntrlplay:before {
	content: "\e600";
}
.ult-vid-cntrlpause:before {
	content: "\e601";
}
