<?php

declare( strict_types=1 );

namespace Usefulteam\FileExplorer\Api;

use Usefulteam\FileExplorer\Api\Controllers\FileSystemController;

final readonly class ApiRoutes
{
	/**
	 * The route's namespace + version.
	 *
	 * @var string
	 */
	private readonly string $root;

	/**
	 * @param string $namespace The API namespace.
	 * @param string $version   The API version.
	 */
	public function __construct(
		private readonly string $namespace = 'file-explorer',
		private readonly string $version = 'v1'
	)
	{
		$this->root = $this->namespace . '/' . $this->version;

		add_action( 'rest_api_init', [ $this, 'handleRestApiInit' ] );
	}

	public function handleRestApiInit(): void
	{
		$this->addCorsSupport();
		$this->registerRoutes();
	}

	/**
	 * Add CORs suppot to the request.
	 */
	public function addCorsSupport(): void
	{
		$allowed_headers = 'X-Requested-With, X-WP-Nonce, Content-Type, Content-Disposition, Accept, Origin, Authorization, Cookie';

		header( sprintf( 'Access-Control-Allow-Headers: %s', $allowed_headers ) );

		header( 'Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS' );
		header( 'Access-Control-Allow-Credentials: true' );
		header( 'Access-Control-Expose-Headers: X-WP-Total, X-WP-TotalPages, Link, Content-Disposition' );
		header( 'Access-Control-Allow-Origin: ' . site_url() );
	}

	/**
	 * Register REST API routes.
	 */
	public function registerRoutes(): void
	{
		$fs_controller = new FileSystemController();

		// List files (optionally in a directory via query param)
		register_rest_route( $this->root, '/files', [
			'methods'             => 'GET',
			'callback'            => [$fs_controller, 'list'],
			'permission_callback' => fn() => current_user_can( 'upload_files' ),
		] );

		// Upload a single file
		register_rest_route( $this->root, '/files', [
			'methods'             => 'POST',
			'callback'            => [$fs_controller, 'upload'],
			'permission_callback' => fn() => current_user_can( 'upload_files' ),
		] );

		// Upload multiple files
		register_rest_route( $this->root, '/files/batch', [
			'methods'             => 'POST',
			'callback'            => [$fs_controller, 'uploadMultiple'],
			'permission_callback' => fn() => current_user_can( 'upload_files' ),
		] );

		// Rename a file or folder
		register_rest_route( $this->root, '/files/(?P<file>.+)/name', [
			'methods'             => 'PATCH',
			'callback'            => [$fs_controller, 'rename'],
			'permission_callback' => fn() => current_user_can( 'upload_files' ),
			'args'                => [
				'file' => [
					'description'       => 'Path of the file or folder to rename.',
					'type'              => 'string',
					'required'          => true,
					'sanitize_callback' => 'sanitize_text_field',
					'validate_callback' => fn( $param ) => is_string( $param ) && trim( $param ) !== '',
				],
			],
		] );

		// Delete a file or folder
		register_rest_route( $this->root, '/files/(?P<file>.+)', [
			'methods'             => 'DELETE',
			'callback'            => [$fs_controller, 'delete'],
			'permission_callback' => fn() => current_user_can( 'upload_files' ),
			'args'                => [
				'file' => [
					'description'       => 'Path of the file or folder to delete.',
					'type'              => 'string',
					'required'          => true,
					'sanitize_callback' => 'sanitize_text_field',
					'validate_callback' => fn( $param ) => is_string( $param ) && trim( $param ) !== '',
				],
			],
		] );

		// Move a file or folder
		register_rest_route( $this->root, '/files/(?P<file>.+)/move', [
			'methods'             => 'PATCH',
			'callback'            => [$fs_controller, 'move'],
			'permission_callback' => fn() => current_user_can( 'upload_files' ),
			'args'                => [
				'file' => [
					'description'       => 'Path of the file or folder to move.',
					'type'              => 'string',
					'required'          => true,
					'sanitize_callback' => 'sanitize_text_field',
					'validate_callback' => fn( $param ) => is_string( $param ) && trim( $param ) !== '',
				],
			],
		] );

		// Copy a file or folder
		register_rest_route( $this->root, '/files/(?P<file>.+)/copy', [
			'methods'             => 'POST',
			'callback'            => [$fs_controller, 'copy'],
			'permission_callback' => fn() => current_user_can( 'upload_files' ),
			'args'                => [
				'file' => [
					'description'       => 'Path of the file or folder to copy.',
					'type'              => 'string',
					'required'          => true,
					'sanitize_callback' => 'sanitize_text_field',
					'validate_callback' => fn( $param ) => is_string( $param ) && trim( $param ) !== '',
				],
			],
		] );

		// Archive files (assumed to come from request body)
		register_rest_route( $this->root, '/archives', [
			'methods'             => 'POST',
			'callback'            => [$fs_controller, 'archive'],
			'permission_callback' => fn() => current_user_can( 'upload_files' ),
		] );

		// Extract a specific archive
		register_rest_route( $this->root, '/archives/(?P<archive>.+)/extract', [
			'methods'             => 'POST',
			'callback'            => [$fs_controller, 'extractArchive'],
			'permission_callback' => fn() => current_user_can( 'upload_files' ),
			'args'                => [
				'archive' => [
					'description'       => 'Path of the archive file to extract.',
					'type'              => 'string',
					'required'          => true,
					'sanitize_callback' => 'sanitize_text_field',
					'validate_callback' => fn( $param ) => is_string( $param ) && trim( $param ) !== '',
				],
			],
		] );

		// Change file permissions
		register_rest_route( $this->root, '/files/(?P<file>.+)/permissions', [
			'methods'             => 'PATCH',
			'callback'            => [$fs_controller, 'chmod'],
			'permission_callback' => fn() => current_user_can( 'upload_files' ),
			'args'                => [
				'file' => [
					'description'       => 'Path of the file to change permissions for.',
					'type'              => 'string',
					'required'          => true,
					'sanitize_callback' => 'sanitize_text_field',
					'validate_callback' => fn( $param ) => is_string( $param ) && trim( $param ) !== '',
				],
			],
		] );
	}
}
