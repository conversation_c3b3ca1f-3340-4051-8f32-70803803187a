<?php

declare( strict_types=1 );

namespace Usefulteam\FileExplorer\Api\Controllers;

use Usefulteam\FileExplorer\Modules\Base\Response\BaseResponse;
use Usefulteam\FileExplorer\Modules\Fs\Response\ArchiveResponse;
use Usefulteam\FileExplorer\Modules\Fs\Response\ChmodResponse;
use Usefulteam\FileExplorer\Modules\Fs\Response\DirListResponse;
use Usefulteam\FileExplorer\Modules\Fs\Response\ExtractResponse;
use Usefulteam\FileExplorer\Modules\Fs\Response\UploadMultipleResponse;
use Usefulteam\FileExplorer\Modules\Fs\Response\UploadResponse;
use Usefulteam\FileExplorer\Modules\Fs\Services\FileSystemService;
use WP_Error;
use WP_REST_Request;
use WP_REST_Response;

final class FileSystemController extends BaseController
{
	private FileSystemService $fsService;

	public function __construct()
	{
		$this->fsService = new FileSystemService();
	}

	public function none( WP_REST_Request $request ): WP_REST_Response
	{
		return new WP_REST_Response(
			new BaseResponse(
				success: false,
				message: 'Nothing to do here',
			)
		);
	}

	/**
	 * List files/directories in given path.
	 *
	 * @param WP_REST_Request $request The request object.
	 * @return WP_REST_Response<DirListResponse>
	 */
	public function list( WP_REST_Request $request ): WP_REST_Response
	{
		$path = sanitize_text_field( $request->get_param( 'path' ) ?? '' );

		$result = $this->fsService->list( $path );
		$status = $result instanceof WP_Error ? $this->wpErrorToHttpStatus( $result ) : 200;

		return new WP_REST_Response(
			new DirListResponse(
				success: ! ( $result instanceof WP_Error ),
				message: $result instanceof WP_Error ? $result->get_error_message() : 'Successfully retrieved files and directories.',
				data: $result instanceof WP_Error ? null : $result,
			),
			$status,
		);
	}

	/**
	 * Rename a file or directory.
	 *
	 * @param WP_REST_Request $request The request object.
	 * @return WP_REST_Response<BaseResponse>
	 */
	public function rename( WP_REST_Request $request ): WP_REST_Response
	{
		$old = sanitize_text_field( $request->get_param( 'file' ) ); // from route param
		$new = sanitize_text_field( $request->get_param( 'new' ) );

		$result = $this->fsService->rename( $old, $new );
		$status = $result instanceof WP_Error ? $this->wpErrorToHttpStatus( $result ) : 200;

		return new WP_REST_Response(
			new BaseResponse(
				success: ! ( $result instanceof WP_Error ),
				message: $result instanceof WP_Error ? $result->get_error_message() : 'Successfully renamed file or directory.',
			),
			$status,
		);
	}

	/**
	 * Delete a file or directory.
	 *
	 * @param WP_REST_Request $request The request object.
	 * @return WP_REST_Response<BaseResponse>
	 */
	public function delete( WP_REST_Request $request ): WP_REST_Response
	{
		$path = sanitize_text_field( $request->get_param( 'file' ) ); // from route param

		$result = $this->fsService->delete( $path );
		$status = $result instanceof WP_Error ? $this->wpErrorToHttpStatus( $result ) : 200;

		return new WP_REST_Response(
			new BaseResponse(
				success: ! ( $result instanceof WP_Error ),
				message: $result instanceof WP_Error ? $result->get_error_message() : 'Successfully deleted file or directory.',
			),
			$status,
		);
	}

	/**
	 * Copy a file or directory.
	 *
	 * @param WP_REST_Request $request The request object.
	 * @return WP_REST_Response<BaseResponse>
	 */
	public function copy( WP_REST_Request $request ): WP_REST_Response
	{
		$source      = sanitize_text_field( $request->get_param( 'file' ) ); // from route param
		$destination = sanitize_text_field( $request->get_param( 'destination' ) );

		$result = $this->fsService->copy( $source, $destination );
		$status = $result instanceof WP_Error ? $this->wpErrorToHttpStatus( $result ) : 200;

		return new WP_REST_Response(
			new BaseResponse(
				success: ! ( $result instanceof WP_Error ),
				message: $result instanceof WP_Error ? $result->get_error_message() : 'Successfully copied file or directory.',
			),
			$status,
		);
	}

	/**
	 * Move a file or directory.
	 *
	 * @param WP_REST_Request $request The request object.
	 * @return WP_REST_Response<BaseResponse>
	 */
	public function move( WP_REST_Request $request ): WP_REST_Response
	{
		$source      = sanitize_text_field( $request->get_param( 'file' ) ); // from route param
		$destination = sanitize_text_field( $request->get_param( 'destination' ) );

		$result = $this->fsService->move( $source, $destination );
		$status = $result instanceof WP_Error ? $this->wpErrorToHttpStatus( $result ) : 200;

		return new WP_REST_Response(
			new BaseResponse(
				success: ! ( $result instanceof WP_Error ),
				message: $result instanceof WP_Error ? $result->get_error_message() : 'Successfully moved file or directory.',
			),
			$status,
		);
	}

	/**
	 * Upload a file.
	 *
	 * @param WP_REST_Request $request The request object.
	 * @return WP_REST_Response<UploadResponse>
	 */
	public function upload( WP_REST_Request $request ): WP_REST_Response
	{
		$files = $request->get_file_params();
		$file  = isset( $files['file'] ) && is_array( $files['file'] ) ? $files['file'] : null;

		$result = $this->fsService->upload( $file );
		$status = $result instanceof WP_Error ? $this->wpErrorToHttpStatus( $result ) : 200;

		return new WP_REST_Response(
			new UploadResponse(
				success: ! ( $result instanceof WP_Error ),
				message: $result instanceof WP_Error ? $result->get_error_message() : 'Successfully moved file or directory.',
				data: $result instanceof WP_Error ? null : $result,
			),
			$status,
		);
	}

	/**
	 * Upload multiple files.
	 *
	 * @param WP_REST_Request $request The request object.
	 * @return WP_REST_Response<UploadMultipleResponse>
	 */
	public function uploadMultiple( WP_REST_Request $request ): WP_REST_Response
	{
		$files = $request->get_file_params();

		$result = $this->fsService->uploadMultiple( $files );
		$status = 200;

		if ( $result instanceof WP_Error ) {
			$status = $this->wpErrorToHttpStatus( $result );
		}

		if ( is_array( $result ) ) {
			$all_errors = array_filter( $result, fn( $item ) => $item instanceof WP_Error );
			
			if ( count( $all_errors ) === count( $result ) ) {
				$status = 500;
			} else {
				// 207 Multi-Status (some successful, some failed)
				$status = 207;
			}
		}

		return new WP_REST_Response(
			new UploadMultipleResponse(
				success: ! ( $result instanceof WP_Error ),
				message: $result instanceof WP_Error ? $result->get_error_message() : 'Successfully moved file or directory.',
				data: $result,
			),
			$status,
		);
	}

	/**
	 * Archive files and directories.
	 *
	 * @param WP_REST_Request $request The request object.
	 * @return WP_REST_Response<ArchiveResponse>
	 */
	public function archive( WP_REST_Request $request ): WP_REST_Response
	{
		$paths = $request->get_param( 'paths' );
		$paths = is_array( $paths ) && !empty( $paths )
			? array_values( array_map( fn( $path ) => is_string( $path ) ? sanitize_text_field( $path ) : '', $paths ) )
			: [];

		$destination = sanitize_text_field( $request->get_param( 'destination' ) );

		$result = $this->fsService->archive( $paths, $destination );
		$status = $result instanceof WP_Error ? $this->wpErrorToHttpStatus( $result ) : 200;

		return new WP_REST_Response(
			new ArchiveResponse(
				success: ! ( $result instanceof WP_Error ),
				message: $result instanceof WP_Error ? $result->get_error_message() : 'Successfully moved file or directory.',
				data: $result instanceof WP_Error ? null : $result,
			),
			$status,
		);
	}

	/**
	 * Extract a zip archive to a directory.
	 *
	 * @param WP_REST_Request $request The request object.
	 * @return WP_REST_Response<ExtractResponse>
	 */
	public function extractArchive( WP_REST_Request $request ): WP_REST_Response
	{
		$zipPath     = sanitize_text_field( $request->get_param( 'archive' ) ); // from route param
		$destination = sanitize_text_field( $request->get_param( 'destination' ) ?? '' );

		$result = $this->fsService->extractArchive( $zipPath, $destination );
		$status = $result instanceof WP_Error ? $this->wpErrorToHttpStatus( $result ) : 200;

		return new WP_REST_Response(
			new ExtractResponse(
				success: ! ( $result instanceof WP_Error ),
				message: $result instanceof WP_Error ? $result->get_error_message() : 'Successfully extracted archive.',
				data: $result instanceof WP_Error ? null : $result,
			),
			$status,
		);
	}

	/**
	 * Change the permissions of a file or directory.
	 *
	 * @param WP_REST_Request $request The request object.
	 * @return WP_REST_Response<ChmodResponse>
	 */
	public function chmod( WP_REST_Request $request ): WP_REST_Response
	{
		$path = sanitize_text_field( $request->get_param( 'file' ) ); // from route param
		$mode = sanitize_text_field( strval( $request->get_param( 'mode' ) ) );

		$result = $this->fsService->chmod( $path, $mode );
		$status = $result instanceof WP_Error ? $this->wpErrorToHttpStatus( $result ) : 200;

		return new WP_REST_Response(
			new ChmodResponse(
				success: ! ( $result instanceof WP_Error ),
				message: $result instanceof WP_Error ? $result->get_error_message() : 'Successfully changed permissions.',
				data: $result instanceof WP_Error ? null : $result,
			),
			$status,
		);
	}
}
