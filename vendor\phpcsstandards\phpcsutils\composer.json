{"name": "phpcsstandards/phpcsutils", "description": "A suite of utility functions for use with PHP_CodeSniffer", "type": "phpcodesniffer-standard", "keywords": ["phpcs", "phpcbf", "standards", "static analysis", "php_codesniffer", "phpcs3", "phpcs4", "tokens", "utility", "phpcodesniffer-standard"], "license": "LGPL-3.0-or-later", "homepage": "https://phpcsutils.com/", "authors": [{"name": "<PERSON>", "role": "lead", "homepage": "https://github.com/jrfnl"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSUtils/graphs/contributors"}], "support": {"issues": "https://github.com/PHPCSStandards/PHPCSUtils/issues", "source": "https://github.com/PHPCSStandards/PHPCSUtils", "docs": "https://phpcsutils.com/", "security": "https://github.com/PHPCSStandards/PHPCSUtils/security/policy"}, "require": {"php": ">=5.4", "squizlabs/php_codesniffer": "^3.13.0 || ^4.0", "dealerdirect/phpcodesniffer-composer-installer": "^0.4.1 || ^0.5 || ^0.6.2 || ^0.7 || ^1.0"}, "require-dev": {"ext-filter": "*", "phpcsstandards/phpcsdevcs": "^1.1.6", "php-parallel-lint/php-parallel-lint": "^1.4.0", "php-parallel-lint/php-console-highlighter": "^1.0", "yoast/phpunit-polyfills": "^1.1.0 || ^2.0.0 || ^3.0.0"}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"classmap": ["PHPCSUtils/"]}, "autoload-dev": {"psr-4": {"PHPCSUtils\\Tests\\": "Tests/", "PHPCSUtils\\GHPages\\": ".github/GHPages/"}}, "extra": {"branch-alias": {"dev-stable": "1.x-dev", "dev-develop": "1.x-dev"}}, "scripts": {"lint-lt72": ["@php ./vendor/php-parallel-lint/php-parallel-lint/parallel-lint . --show-deprecated -e php --exclude vendor --exclude .git --exclude .github/GHPages"], "lint": ["@php ./vendor/php-parallel-lint/php-parallel-lint/parallel-lint . -e php --exclude vendor --exclude .git"], "checkcs": ["@php ./vendor/squizlabs/php_codesniffer/bin/phpcs"], "fixcs": ["@php ./vendor/squizlabs/php_codesniffer/bin/phpcbf"], "test": ["@php ./vendor/phpunit/phpunit/phpunit --no-coverage"], "test10": ["@php ./vendor/phpunit/phpunit/phpunit -c phpunit10.xml.dist --no-coverage"], "coverage": ["@php ./vendor/phpunit/phpunit/phpunit"], "coverage10": ["@php ./vendor/phpunit/phpunit/phpunit -c phpunit10.xml.dist"], "coverage-local": ["@php ./vendor/phpunit/phpunit/phpunit --coverage-html ./build/coverage-html"], "coverage-local10": ["@php ./vendor/phpunit/phpunit/phpunit -c phpunit10.xml.dist --coverage-html ./build/coverage-html"]}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}, "lock": false}}