| Method   | Endpoint                                               | Description                       | Query Params | Example Usage                                                                                 |
| -------- | ------------------------------------------------------ | --------------------------------- | ------------ | --------------------------------------------------------------------------------------------- |
| `GET`    | `/wp-json/file-explorer/v1/files`                      | List files in a directory         | `path`       | `/wp-json/file-explorer/v1/files?path=/uploads`                                               |
| `POST`   | `/wp-json/file-explorer/v1/files`                      | Upload a single file              | –            | `POST` to `/wp-json/file-explorer/v1/files` with `file` in `$_FILES`                          |
| `POST`   | `/wp-json/file-explorer/v1/files/batch`                | Upload multiple files             | –            | `POST` to `/wp-json/file-explorer/v1/files/batch` with multiple `$_FILES`                     |
| `PATCH`  | `/wp-json/file-explorer/v1/files/{file}/name`          | Rename file or folder             | –            | `/wp-json/file-explorer/v1/files/uploads/image.jpg/name` with `new=image-renamed.jpg` in body |
| `DELETE` | `/wp-json/file-explorer/v1/files/{file}`               | Delete file or folder             | –            | `/wp-json/file-explorer/v1/files/uploads/image.jpg`                                           |
| `PATCH`  | `/wp-json/file-explorer/v1/files/{file}/move`          | Move file or folder               | –            | `/wp-json/file-explorer/v1/files/uploads/image.jpg/move` with `destination=/archive/`         |
| `POST`   | `/wp-json/file-explorer/v1/files/{file}/copy`          | Copy file or folder               | –            | `/wp-json/file-explorer/v1/files/uploads/image.jpg/copy` with `destination=/backup/`          |
| `POST`   | `/wp-json/file-explorer/v1/archives`                   | Create zip from files/directories | –            | `POST` to `/wp-json/file-explorer/v1/archives` with `paths[]` and `destination` in body       |
| `POST`   | `/wp-json/file-explorer/v1/archives/{archive}/extract` | Extract zip archive               | –            | `/wp-json/file-explorer/v1/archives/backups/2024.zip/extract` with `destination=/uploads/`    |
| `PATCH`  | `/wp-json/file-explorer/v1/files/{file}/permissions`   | Change file permissions           | –            | `/wp-json/file-explorer/v1/files/uploads/image.jpg/permissions` with `mode=0755`              |

## API Response Examples

### List Files Response
**Endpoint:** `GET /wp-json/file-explorer/v1/files?path=/uploads`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Successfully retrieved files and directories.",
  "data": [
    {
      "name": "image.jpg",
      "prms": "-rw-r--r--",
      "prmsn": "0644",
      "number": 1,
      "owner": "www-data",
      "group": "www-data",
      "size": 245760,
      "lastmodunix": 1703097600,
      "lastmod": "Dec 20 2023",
      "time": "10:00:00",
      "type": "f",
      "files": false
    },
    {
      "name": "documents",
      "prms": "drwxr-xr-x",
      "prmsn": "0755",
      "number": 2,
      "owner": "www-data",
      "group": "www-data",
      "size": 4096,
      "lastmodunix": 1703097600,
      "lastmod": "Dec 20 2023",
      "time": "10:00:00",
      "type": "d",
      "files": false
    }
  ]
}
```

**Error Response (404):**
```json
{
  "success": false,
  "message": "Directory not found.",
  "data": null
}
```

### Upload Single File Response
**Endpoint:** `POST /wp-json/file-explorer/v1/files`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Successfully moved file or directory.",
  "data": {
    "file": "/wp-content/uploads/2024/01/document.pdf",
    "url": "https://example.com/wp-content/uploads/2024/01/document.pdf",
    "type": "application/pdf"
  }
}
```

**Error Response (400):**
```json
{
  "success": false,
  "message": "No file uploaded or file upload failed.",
  "data": null
}
```

### Upload Multiple Files Response
**Endpoint:** `POST /wp-json/file-explorer/v1/files/batch`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Successfully moved file or directory.",
  "data": [
    {
      "file": "/wp-content/uploads/2024/01/image1.jpg",
      "url": "https://example.com/wp-content/uploads/2024/01/image1.jpg",
      "type": "image/jpeg"
    },
    {
      "file": "/wp-content/uploads/2024/01/image2.png",
      "url": "https://example.com/wp-content/uploads/2024/01/image2.png",
      "type": "image/png"
    }
  ]
}
```

**Partial Success Response (207):**
```json
{
  "success": true,
  "message": "Successfully moved file or directory.",
  "data": [
    {
      "file": "/wp-content/uploads/2024/01/image1.jpg",
      "url": "https://example.com/wp-content/uploads/2024/01/image1.jpg",
      "type": "image/jpeg"
    },
    {
      "code": "upload_failed",
      "message": "File upload failed: Invalid file type.",
      "data": null
    }
  ]
}
```

### Rename File Response
**Endpoint:** `PATCH /wp-json/file-explorer/v1/files/uploads/image.jpg/name`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Successfully renamed file or directory.",
  "data": null
}
```

**Error Response (404):**
```json
{
  "success": false,
  "message": "File or directory not found.",
  "data": null
}
```

### Delete File Response
**Endpoint:** `DELETE /wp-json/file-explorer/v1/files/uploads/image.jpg`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Successfully deleted file or directory.",
  "data": null
}
```

**Error Response (403):**
```json
{
  "success": false,
  "message": "Permission denied: Cannot delete file.",
  "data": null
}
```

### Move File Response
**Endpoint:** `PATCH /wp-json/file-explorer/v1/files/uploads/image.jpg/move`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Successfully moved file or directory.",
  "data": null
}
```

**Error Response (400):**
```json
{
  "success": false,
  "message": "Destination directory does not exist.",
  "data": null
}
```

### Copy File Response
**Endpoint:** `POST /wp-json/file-explorer/v1/files/uploads/image.jpg/copy`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Successfully copied file or directory.",
  "data": null
}
```

**Error Response (409):**
```json
{
  "success": false,
  "message": "File already exists at destination.",
  "data": null
}
```

### Create Archive Response
**Endpoint:** `POST /wp-json/file-explorer/v1/archives`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Successfully moved file or directory.",
  "data": {
    "file": "/wp-content/uploads/archives/backup-2024-01-20.zip",
    "url": "https://example.com/wp-content/uploads/archives/backup-2024-01-20.zip",
    "type": "application/zip"
  }
}
```

**Error Response (500):**
```json
{
  "success": false,
  "message": "Failed to create archive: Insufficient disk space.",
  "data": null
}
```

### Extract Archive Response
**Endpoint:** `POST /wp-json/file-explorer/v1/archives/backups/2024.zip/extract`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Successfully extracted archive.",
  "data": {
    "dir": "/wp-content/uploads/extracted/2024",
    "items": [
      {
        "type": "file",
        "path": "/wp-content/uploads/extracted/2024/document.pdf"
      },
      {
        "type": "dir",
        "path": "/wp-content/uploads/extracted/2024/images"
      },
      {
        "type": "file",
        "path": "/wp-content/uploads/extracted/2024/images/photo.jpg"
      }
    ]
  }
}
```

**Error Response (400):**
```json
{
  "success": false,
  "message": "Invalid archive file or corrupted archive.",
  "data": null
}
```

### Change File Permissions Response
**Endpoint:** `PATCH /wp-json/file-explorer/v1/files/uploads/image.jpg/permissions`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Successfully changed permissions.",
  "data": {
    "file": "/wp-content/uploads/image.jpg",
    "old_permissions": "0644",
    "new_permissions": "0755"
  }
}
```

**Error Response (403):**
```json
{
  "success": false,
  "message": "Permission denied: Cannot change file permissions.",
  "data": null
}
```
