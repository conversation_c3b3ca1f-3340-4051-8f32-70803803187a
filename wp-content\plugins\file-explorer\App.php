<?php

declare( strict_types=1 );

namespace Usefulteam\FileExplorer;

use Usefulteam\FileExplorer\Modules\Fs\FsSetup;
use Usefulteam\FileExplorer\Modules\Spa\SpaSetup;

final readonly class App
{
	public function __construct()
	{
		register_activation_hook( Config::pluginFile(), [ $this, 'handleActivationHook' ] );
		add_action( 'plugins_loaded', [$this, 'setupModules'] );
		add_action( 'init', [$this, 'handleOnInit'] );
	}

	public function handleActivationHook(): void
	{
		//
	}

	public function setupModules(): void
	{
		new FsSetup();
		new SpaSetup();
	}

	public function handleOnInit(): void
	{
		//
	}
}
