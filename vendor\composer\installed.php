<?php return array(
    'root' => array(
        'name' => 'contactjavas/suarwoodtable',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'contactjavas/modern-wpcs-ruleset' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '396d67fd00e9bf71652eb0968d7394bdc3062984',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../contactjavas/modern-wpcs-ruleset',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'dev_requirement' => true,
        ),
        'contactjavas/modern-wpcs-standard' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '7da2bdfbd26fc4e69183497c078017b9c712ba5e',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../contactjavas/modern-wpcs-standard',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'dev_requirement' => true,
        ),
        'contactjavas/suarwoodtable' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dealerdirect/phpcodesniffer-composer-installer' => array(
            'pretty_version' => 'v1.1.1',
            'version' => '1.1.1.0',
            'reference' => '6e0fa428497bf560152ee73ffbb8af5c6a56b0dd',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../dealerdirect/phpcodesniffer-composer-installer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpcsstandards/phpcsextra' => array(
            'pretty_version' => 'dev-develop',
            'version' => 'dev-develop',
            'reference' => 'fa4b8d051e278072928e32d817456a7fdb57b6ca',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../phpcsstandards/phpcsextra',
            'aliases' => array(
                0 => '1.x-dev',
            ),
            'dev_requirement' => true,
        ),
        'phpcsstandards/phpcsutils' => array(
            'pretty_version' => 'dev-develop',
            'version' => 'dev-develop',
            'reference' => 'd3b4a634ca279dc06ca56a79df6414d542c25ffc',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../phpcsstandards/phpcsutils',
            'aliases' => array(
                0 => '1.x-dev',
            ),
            'dev_requirement' => true,
        ),
        'sirbrillig/phpcs-import-detection' => array(
            'pretty_version' => 'v1.3.3',
            'version' => '1.3.3.0',
            'reference' => '7035ef6f3a15db182b59664d2c060918aa827e16',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../sirbrillig/phpcs-import-detection',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sirbrillig/phpcs-variable-analysis' => array(
            'pretty_version' => '2.x-dev',
            'version' => '2.9999999.9999999.9999999-dev',
            'reference' => 'fb1275cb32208d5bed108292c45bbef216818a33',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../sirbrillig/phpcs-variable-analysis',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'squizlabs/php_codesniffer' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '368817d890b7b872b39ff188f739434244bb240c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../squizlabs/php_codesniffer',
            'aliases' => array(
                0 => '3.x-dev',
            ),
            'dev_requirement' => true,
        ),
        'wp-coding-standards/wpcs' => array(
            'pretty_version' => '3.1.0',
            'version' => '3.1.0.0',
            'reference' => '9333efcbff231f10dfd9c56bb7b65818b4733ca7',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../wp-coding-standards/wpcs',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
    ),
);
