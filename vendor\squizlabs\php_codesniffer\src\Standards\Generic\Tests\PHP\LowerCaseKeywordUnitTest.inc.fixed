<?php
// A few keywords. Obviously not valid syntax.
class Something extends SomethingElse implements Whatever {}
abstract public private protected function whatever() {}
const array();
foreach ($var as $var) { exit; }
if ($a and $b or $c xor $d) { die; } elseif( $var ) { } else {}
goto a;

class Something extends SomethingElse implements Whatever {}
abstract public private protected function whatever() {}
const array();
foreach ($var as $var) { exit; }
if ($a and $b or $c xor $d) { die; } elseif( $var ) { } else {}
goto a;
$b = function (Array $a) {};
const PRIVATE;
HttpStatus::CONTINUE;
$c = function ($f) {
    yield $f;
    yield from fun();
};
class X extends Y {
    public function m() {
        parent::m();
    }
    public function n() {
        self::n();
    }
}
fn ($x) => $x;
$r = match ($x) {
    1 => 1,
    2 => 2,
    default, => 3,
};

class Reading {
    public readonly int $var;
}

enum ENUM: string
{
    case HEARTS;
}

new class {};
new class extends stdClass {};
new class {};

if (isset($a) && !empty($a)) { unset($a); }
if (isset($a) && !empty($a)) { unset($a); }
eval('foo');
eval('foo');

$c = function() {
    yield /*comment*/ from fun();
    yield
    /*comment*/
    from fun();
}

class AsymmetricVisibility {
    public(set) int $correctPublicSet;
    protected(set) int $correctProtectedSet;
    private(set) int $correctPrivateSet;

    public(set) int $wrongCasePublic;
    protected(set) array $wrongCaseProtected;
    protected(set) int $wrongCaseProtectedSet;
    private(set) int $wrongCasePrivate;
    private(set) readonly ?string $wrongCasePrivateSet;
}

__HALT_COMPILER(); // An exception due to phar support.
function
