<?php

declare( strict_types=1 );

namespace Usefulteam\FileExplorer\Modules\Fs\Services;

use Usefulteam\FileExplorer\Modules\Fs\Dto\ArchiveResultData;
use Usefulteam\FileExplorer\Modules\Fs\Dto\DirListItemData;
use Usefulteam\FileExplorer\Modules\Fs\Dto\ExtractedItemData;
use Usefulteam\FileExplorer\Modules\Fs\Dto\ExtractResultData;
use Usefulteam\FileExplorer\Modules\Fs\Dto\UploadResultData;
use WP_Error;
use WP_Filesystem_Base;
use ZipArchive;

final class FileSystemService
{
	private readonly WP_Filesystem_Base $wpFs;

	private readonly string $realBasePath;

	private readonly string $realAllowedPath;

	/** @var array<string, string> */
	private readonly array $allowedCapabilities;

	private ?string $uploadDir = null;

	public function __construct()
	{
		if ( ! function_exists( 'request_filesystem_credentials' ) ) {
			require_once ABSPATH . 'wp-admin/includes/file.php';
		}

		/** @var WP_Filesystem_Base $wp_filesystem */
		global $wp_filesystem;

		if ( ! $wp_filesystem ) {
			WP_Filesystem();
		}
	
		$this->wpFs = $wp_filesystem;

		$this->realBasePath = realpath( ABSPATH );

		// Normalize slashes.
		$this->realBasePath = rtrim( str_replace( '\\', '/', $this->realBasePath ), '/' );

		// For now, ABSPATH is the allowed path (not changeable for now).
		$this->realAllowedPath = $this->realBasePath;

		// Only users with 'manage_options' can access the file manager for now.
		$this->allowedCapabilities = [
			'all' => 'manage_options',
		];
	}

	/**
	 * List files/directories in given path.
	 *
	 * @param string $path Path to list.
	 * @return DirListItemData[]|WP_Error List of files/directories or WP_Error on failure.
	 */
	public function list( string $path ): array|WP_Error
	{
		if ( !$this->capabilityAllowed( 'list_dir' ) ) {
			return new WP_Error(
				code: 'capability_not_allowed',
				message: 'You are not allowed to list directories.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->pathAllowed( $path ) ) {
			return new WP_Error(
				code: 'unsafe_path',
				message: 'Path is not within the allowed directory.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->wpFs->is_dir( $path ) ) {
			return new WP_Error(
				code: 'not_a_directory',
				message: 'Provided path is not a directory.',
				data: ['status' => 400]
			);
		}

		$files = $this->wpFs->dirlist( $path );

		if ( ! is_array( $files ) ) {
			return new WP_Error(
				code: 'list_failed',
				message: 'Failed to list files in the directory.',
				data: ['status' => 500]
			);
		}

		return array_map( fn( array $file ) => DirListItemData::fromArray( $file ), $files );
	}

	/**
	 * Rename a file or directory.
	 */
	public function rename( string $old, string $new ): true|WP_Error
	{
		if ( !$this->capabilityAllowed( 'rename_file' ) ) {
			return new WP_Error(
				code: 'capability_not_allowed',
				message: 'You are not allowed to rename files or directories.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->pathAllowed( $old ) ) {
			return new WP_Error(
				code: 'unsafe_path',
				message: 'Source path is not within the allowed directory.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->pathAllowed( $new ) ) {
			return new WP_Error(
				code: 'unsafe_path',
				message: 'Destination path is not within the allowed directory.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->wpFs->exists( $old ) ) {
			return new WP_Error(
				code: 'not_found',
				message: 'Source path not found.',
				data: ['status' => 404]
			);
		}

		if ( $this->wpFs->exists( $new ) ) {
			return new WP_Error(
				code: 'target_exists',
				message: 'Target path already exists.',
				data: ['status' => 409],
			);
		}

		$result = $this->wpFs->move( $old, $new );

		return $result ?: new WP_Error( 'rename_failed', 'Failed to rename.', ['status' => 500] );
	}

	/**
	 * Delete a file or directory.
	 */
	public function delete( string $path ): true|WP_Error
	{
		if ( !$this->capabilityAllowed( 'delete_file' ) ) {
			return new WP_Error(
				code: 'capability_not_allowed',
				message: 'You are not allowed to delete files or directories.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->pathAllowed( $path ) ) {
			return new WP_Error(
				code: 'unsafe_path',
				message: 'Path is not within the allowed directory.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->wpFs->exists( $path ) ) {
			return new WP_Error( 'not_found', 'File or directory not found.', ['status' => 404] );
		}

		$is_dir  = $this->wpFs->is_dir( $path );
		$success = $this->wpFs->delete( $path, $is_dir );

		return $success ?: new WP_Error(
			code: 'delete_failed',
			message: 'Failed to delete.',
			data: ['status' => 500],
		);
	}

	public function copy( string $source, string $destination ): true|WP_Error
	{
		if ( !$this->capabilityAllowed( 'copy_file' ) ) {
			return new WP_Error(
				code: 'capability_not_allowed',
				message: 'You are not allowed to copy files or directories.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->pathAllowed( $source ) ) {
			return new WP_Error(
				code: 'unsafe_path',
				message: 'Source path is not within the allowed directory.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->pathAllowed( $destination ) ) {
			return new WP_Error(
				code: 'unsafe_path',
				message: 'Destination path is not within the allowed directory.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->wpFs->exists( $source ) ) {
			return new WP_Error(
				code: 'not_found',
				message: 'Source does not exist.',
				data: ['status' => 404]
			);
		}

		if ( $this->wpFs->exists( $destination ) ) {
			return new WP_Error(
				code: 'target_exists',
				message: 'Destination already exists.',
				data: ['status' => 409]
			);
		}

		$is_dir = $this->wpFs->is_dir( $source );

		if ( $is_dir ) {
			return $this->copyDir( $source, $destination );
		}

		$contents = $this->wpFs->get_contents( $source );

		if ( $contents === false ) {
			return new WP_Error(
				code: 'read_failed',
				message: 'Failed to read source file.',
				data: ['status' => 500]
			);
		}

		$result = $this->wpFs->put_contents( $destination, $contents, FS_CHMOD_FILE );

		return $result ?: new WP_Error(
			code: 'copy_failed',
			message: 'Failed to copy file.',
			data: ['status' => 500]
		);
	}

	private function copyDir( string $source, string $destination ): true|WP_Error
	{
		if ( ! $this->wpFs->mkdir( $destination, FS_CHMOD_DIR, true ) ) {
			return new WP_Error(
				code: 'mkdir_failed',
				message: 'Failed to create destination directory.',
				data: ['status' => 500]
			);
		}

		$list = $this->wpFs->dirlist( $source );

		if ( ! is_array( $list ) ) {
			return new WP_Error(
				code: 'read_dir_failed',
				message: 'Failed to read source directory.',
				data: ['status' => 500]
			);
		}

		foreach ( $list as $name => $info ) {
			$src = trailingslashit( $source ) . $name;
			$dst = trailingslashit( $destination ) . $name;

			if ( $info['type'] === 'f' ) {
				$content = $this->wpFs->get_contents( $src );

				if ( $content === false || ! $this->wpFs->put_contents( $dst, $content, FS_CHMOD_FILE ) ) {
					return new WP_Error( 'copy_file_failed', "Failed to copy file: {$name}", ['status' => 500] );
				}
			} elseif ( $info['type'] === 'd' ) {
				$result = $this->copyDir( $src, $dst );

				if ( is_wp_error( $result ) ) return $result;
			}
		}

		return true;
	}

	public function move( string $source, string $destination ): true|WP_Error
	{
		if ( !$this->capabilityAllowed( 'move_file' ) ) {
			return new WP_Error(
				code: 'capability_not_allowed',
				message: 'You are not allowed to move files.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->pathAllowed( $source ) ) {
			return new WP_Error(
				code: 'unsafe_path',
				message: 'Source path is not within the allowed directory.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->pathAllowed( $destination ) ) {
			return new WP_Error(
				code: 'unsafe_path',
				message: 'Destination path is not within the allowed directory.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->wpFs->exists( $source ) ) {
			return new WP_Error( 'not_found', 'Source does not exist.', ['status' => 404] );
		}

		if ( $this->wpFs->exists( $destination ) ) {
			return new WP_Error( 'target_exists', 'Destination already exists.', ['status' => 409] );
		}

		$success = $this->wpFs->move( $source, $destination );

		return $success ?: new WP_Error(
			code: 'move_failed',
			message: 'Failed to move.',
			data: ['status' => 500]
		);
	}

	/**
	 * Upload a file.
	 *
	 * @see https://developer.wordpress.org/reference/functions/wp_handle_upload/
	 * @see https://developer.wordpress.org/reference/functions/_wp_handle_upload/#parameters
	 *
	 * @param array{
	 *     name: string,
	 *     type: string,
	 *     tmp_name: string,
	 *     size: int,
	 *     error: int
	 * }|null $file The file to upload.
	 * @param string|null $destination The destination directory.
	 *
	 * @return UploadResultData|WP_Error
	 */
	public function upload( array $file, ?string $destination = null ): UploadResultData|WP_Error
	{
		if ( !$this->capabilityAllowed( 'upload_file' ) ) {
			return new WP_Error(
				code: 'capability_not_allowed',
				message: 'You are not allowed to upload files.',
				data: ['status' => 403]
			);
		}

		if ( empty( $file ) ) {
			return new WP_Error(
				code: 'no_file',
				message: 'No file uploaded.',
				data: ['status' => 400]
			);
		}

		$use_custom_upload_dir = false;

		if ( !is_null( $destination ) ) {
			if ( ! $this->pathAllowed( $destination ) ) {
				return new WP_Error(
					code: 'unsafe_path',
					message: 'Destination path is not within the allowed directory.',
					data: ['status' => 403]
				);
			}

			$use_custom_upload_dir = true;
		}

		if ( !function_exists( 'wp_handle_upload' ) ) {
			require_once ABSPATH . 'wp-admin/includes/file.php';
		}

		/**
		 * @var array{
		 *     name: string,
		 *     type: string,
		 *     tmp_name: string,
		 *     size: int,
		 *     error: int
		 * } $file
		 */
		$file = $_FILES['file'];

		if ( $use_custom_upload_dir ) {
			$this->uploadDir = $destination;

			// Override upload directory for wp_handle_upload
			add_filter( 'upload_dir', [ $this, 'customUploadDir' ] );
		}

		$overrides = ['test_form' => false];

		/** 
		 * @var array{
		 *     file: string,
		 *     url: string,
		 *     type: string
		 * }|array{
		 *     error: string
		 * } $result 
		 */
		$result = wp_handle_upload( $file, $overrides );

		if ( $use_custom_upload_dir ) {
			$this->uploadDir = null;

			// Remove the filter
			remove_filter( 'upload_dir', [ $this, 'customUploadDir' ] );
		}

		if ( isset( $result['error'] ) ) {
			return new WP_Error(
				code: 'upload_error',
				message: $result['error'],
				data: ['status' => 500]
			);
		}

		return UploadResultData::fromArray( $result );
	}

	/**
	 * Custom upload directory callback.
	 *
	 * @param array{
	 *   path: string,
	 *   url: string,
	 *   subdir: string,
	 *   basedir: string,
	 *   baseurl: string,
	 *   error: string|false
	 * } $uploads The uploads directory data.
	 *
	 * @return array
	 */
	public function customUploadDir( array $uploads ): array
	{
		if ( empty( $this->uploadDir ) ) {
			return $uploads;
		}

		$upload_dir_url = $this->pathToUrl( $this->uploadDir );

		if ( ! $upload_dir_url ) {
			return $uploads;
		}
		
		return [
			'path'    => $this->uploadDir,
			'url'     => $upload_dir_url,
			'subdir'  => '',
			'basedir' => $this->uploadDir,
			'baseurl' => $upload_dir_url,
			'error'   => false
		];
	}

	/**
	 * Upload multiple files.
	 *
	 * @param array{
	 *     name: string,
	 *     type: string,
	 *     tmp_name: string,
	 *     size: int,
	 *     error: int
	 * }[] $files
	 * @param string|null $destination The destination directory.
	 *
	 * @return array<UploadResultData|WP_Error>|WP_Error
	 */
	public function uploadMultiple( array $files, ?string $destination = null ): array|WP_Error
	{
		if ( !$this->capabilityAllowed( 'upload_file' ) ) {
			return new WP_Error(
				code: 'capability_not_allowed',
				message: 'You are not allowed to upload files.',
				data: ['status' => 403]
			);
		}

		if ( empty( $files ) ) {
			return new WP_Error(
				code: 'no_files',
				message: 'No files uploaded.',
				data: ['status' => 400],
			);
		}

		$use_custom_upload_dir = false;

		if ( !is_null( $destination ) ) {
			if ( ! $this->pathAllowed( $destination ) ) {
				return new WP_Error(
					code: 'unsafe_path',
					message: 'Destination path is not within the allowed directory.',
					data: ['status' => 403]
				);
			}

			$use_custom_upload_dir = true;
		}

		if ( !function_exists( 'wp_handle_upload' ) ) {
			require_once ABSPATH . 'wp-admin/includes/file.php';
		}

		if ( $use_custom_upload_dir ) {
			$this->uploadDir = $destination;

			// Override upload directory for wp_handle_upload
			add_filter( 'upload_dir', [ $this, 'customUploadDir' ] );
		}

		/** @var array<UploadResultData|WP_Error> $results */
		$results = [];

		$overrides = ['test_form' => false];

		foreach ( $files['name'] as $i => $name ) {
			$single_file = [
				'name'     => $files['name'][$i],
				'type'     => $files['type'][$i],
				'tmp_name' => $files['tmp_name'][$i],
				'error'    => $files['error'][$i],
				'size'     => $files['size'][$i],
			];

			/** 
			 * @var array{
			 *     file: string,
			 *     url: string,
			 *     type: string
			 * }|array{
			 *     error: string
			 * } $result 
			 */
			$result = wp_handle_upload( $single_file, $overrides );

			$results[] = isset( $result['error'] ) ? new WP_Error(
				code: 'upload_error',
				message: $result['error'],
				data: ['status' => 500]
			) : UploadResultData::fromArray( $result );
		}

		if ( $use_custom_upload_dir ) {
			$this->uploadDir = null;

			// Remove the filter
			remove_filter( 'upload_dir', [ $this, 'customUploadDir' ] );
		}

		return $results;
	}

	/**
	 * Create a zip archive from a list of paths.
	 *
	 * @param string[] $paths List of file and directory paths to archive.
	 * @param string   $destination Path to the destination zip file.
	 *
	 * @return ArchiveResultData|WP_Error
	 */
	public function archive( array $paths, string $destination ): ArchiveResultData|WP_Error
	{
		if ( !$this->capabilityAllowed( 'make_archive' ) ) {
			return new WP_Error(
				code: 'capability_not_allowed',
				message: 'You are not allowed to archive files or directories.',
				data: ['status' => 403]
			);
		}

		if ( empty( $paths ) ) {
			return new WP_Error(
				code: 'invalid_paths',
				message: 'No paths provided for archiving.',
				data: ['status' => 400]
			);
		}

		if ( file_exists( $destination ) ) {
			return new WP_Error(
				code: 'archive_exists',
				message: 'Archive already exists.',
				data: ['status' => 409]
			);
		}

		if ( ! $this->pathAllowed( $destination ) ) {
			return new WP_Error(
				code: 'unsafe_path',
				message: 'Destination path is not within the allowed directory.',
				data: ['status' => 403]
			);
		}

		$zip = new ZipArchive();

		if ( $zip->open( $destination, ZipArchive::CREATE ) !== true ) {
			return new WP_Error(
				code: 'zip_failed',
				message: 'Failed to create ZIP file.',
				data: ['status' => 500]
			);
		}

		foreach ( $paths as $path ) {
			if ( empty( $path ) || !is_string( $path ) ) {
				continue;
			}

			if ( ! $this->wpFs->exists( $path ) ) {
				continue;
			}

			if ( ! $this->pathAllowed( $path ) ) {
				continue;
			}

			if ( $this->wpFs->is_dir( $path ) ) {
				$this->addDirToZip( $zip, $path, basename( $path ) );
			} else {
				$content = $this->wpFs->get_contents( $path );

				if ( $content !== false ) {
					$zip->addFromString( basename( $path ), $content );
				}
			}
		}

		$zip->close();

		return new ArchiveResultData(
			file: $destination,
			url: wp_upload_dir()['baseurl'] . '/' . basename( $destination ),
			type: 'zip'
		);
	}

	private function addDirToZip( ZipArchive $zip, string $dir, string $internalPath ): void
	{
		$list = $this->wpFs->dirlist( $dir );

		if ( ! is_array( $list ) ) return;

		foreach ( $list as $name => $info ) {
			$src = trailingslashit( $dir ) . $name;
			$dst = trailingslashit( $internalPath ) . $name;

			if ( $info['type'] === 'f' ) {
				$content = $this->wpFs->get_contents( $src );

				if ( $content !== false ) {
					$zip->addFromString( $dst, $content );
				}
			} else {
				$zip->addEmptyDir( $dst );
				$this->addDirToZip( $zip, $src, $dst );
			}

		}
	}

	/**
	 * Extract a zip archive to a directory.
	 *
	 * @param string $zipPath Path to the zip file.
	 * @param string $destination Path to the destination directory.
	 *
	 * @return ExtractResultData|WP_Error
	 */
	public function extractArchive( string $zipPath, string $destination ): ExtractResultData|WP_Error
	{
		if ( !$this->capabilityAllowed( 'extract_archive' ) ) {
			return new WP_Error(
				code: 'capability_not_allowed',
				message: 'You are not allowed to extract archives.',
				data: ['status' => 403]
			);
		}

		if ( empty( $zipPath ) ) {
			return new WP_Error(
				code: 'invalid_zip',
				message: 'No ZIP file provided.',
				data: ['status' => 400]
			);
		}

		if ( empty( $destination ) ) {
			return new WP_Error(
				code: 'invalid_destination',
				message: 'No destination provided.',
				data: ['status' => 400]
			);
		}

		if ( ! $this->pathAllowed( $zipPath ) ) {
			return new WP_Error(
				code: 'unsafe_path',
				message: 'Zip path is not within the allowed directory.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->pathAllowed( $destination ) ) {
			return new WP_Error(
				code: 'unsafe_path',
				message: 'Destination path is not within the allowed directory.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->wpFs->exists( $zipPath ) || ! $this->wpFs->is_file( $zipPath ) ) {
			return new WP_Error(
				code: 'zip_missing',
				message: 'ZIP file does not exist.',
				data: ['status' => 404]
			);
		}

		$tmp     = wp_tempnam( basename( $zipPath ) );
		$content = $this->wpFs->get_contents( $zipPath );

		if ( $content === false || file_put_contents( $tmp, $content ) === false ) {
			return new WP_Error(
				code: 'extract_failed',
				message: 'Failed to copy ZIP to temp.',
				data: ['status' => 500]
			);
		}

		$zip = new ZipArchive();

		if ( $zip->open( $tmp ) !== true ) {
			return new WP_Error(
				code: 'zip_open_failed',
				message: 'Failed to open ZIP file.',
				data: ['status' => 500]
			);
		}

		/** @var ExtractedItemData[] $top_level_items */
		$top_level_items = [];

		for ( $i = 0; $i < $zip->numFiles; $i++ ) {
			$entry    = $zip->getNameIndex( $i );
			$fullPath = trailingslashit( $destination ) . $entry;

			// Check if this is a top-level item (no parent directory in the path)
			$is_top_level = strpos( trim( $entry, '/' ), '/' ) === false;

			if ( str_ends_with( $entry, '/' ) ) {
				$this->wpFs->mkdir( $fullPath, FS_CHMOD_DIR, true );

				// Add to top-level items if it's a top-level directory
				if ( $is_top_level ) {
					$top_level_items[] = new ExtractedItemData(
						type: 'dir',
						path: $fullPath
					);
				}
			} else {
				$content = $zip->getFromIndex( $i );
				$this->wpFs->put_contents( $fullPath, $content, FS_CHMOD_FILE );

				// Add to top-level items if it's a top-level file
				if ( $is_top_level ) {
					$top_level_items[] = new ExtractedItemData(
						type: 'file',
						path: $fullPath
					);
				}
			}
		}

		$zip->close();
		unlink( $tmp );

		return new ExtractResultData(
			dir: $destination,
			items: $top_level_items,
		);
	}

	/**
	 * Change the permissions of a file or directory.
	 *
	 * @param string $path Path to the file or directory.
	 * @param int|string $mode Octal permission mode. Accepts mode like 0755, 755, '0755', '755'(will be converted to octal).
	 * @param bool $recursive Whether to apply to subdirectories and files.
	 *
	 * @return string|WP_Error The permissions in octal-string format when successful, WP_Error otherwise.
	 */
	public function chmod( string $path, int|string $mode, bool $recursive = false ): string|WP_Error
	{
		if ( !$this->capabilityAllowed( 'set_chmod' ) ) {
			return new WP_Error(
				code: 'capability_not_allowed',
				message: 'You are not allowed to set file or directory permissions.',
				data: ['status' => 403]
			);
		}

		if ( empty( $path ) ) {
			return new WP_Error(
				code: 'invalid_path',
				message: 'No path provided.',
				data: ['status' => 400]
			);
		}

		if ( ! $this->pathAllowed( $path ) ) {
			return new WP_Error(
				code: 'unsafe_path',
				message: 'Path is not within the allowed directory.',
				data: ['status' => 403]
			);
		}

		if ( ! $this->wpFs->exists( $path ) ) {
			return new WP_Error(
				code: 'not_found',
				message: 'Path does not exist.',
				data: ['status' => 404]
			);
		}

		// Normalize permission mode to octal integer
		if ( is_string( $mode ) ) {
			// '0755' or '755' → 0755
			$mode = octdec( $mode );
		} elseif ( is_int( $mode ) ) {
			if ( $mode <= 777 && $mode < 512 ) {
				// 755 → 0755
				$mode = octdec( (string) $mode );
			}
		}

		// Validate range (0 to 07777)
		if ( $mode < 0 || $mode > 0o7777 ) {
			return new WP_Error(
				code: 'invalid_permission',
				message: "Invalid permission value: $mode",
				data: ['status' => 400]
			);
		}

		$result = $this->wpFs->chmod( $path, $mode, $recursive );

		if ( !$result ) {
			return new WP_Error(
				code: 'chmod_failed',
				message: 'Failed to change permissions to ' . sprintf( '%04o', $mode ) . '.',
				data: ['status' => 500]
			);
		}

		return sprintf( '%04o', $mode );
	}

	/**
	 * Convert an absolute directory path to a URL.
	 *
	 * This function works for paths inside the WordPress installation only.
	 *
	 * @param string $path Absolute directory path.
	 * @return string|null Corresponding URL, or null if not inside WordPress.
	 */
	private function pathToUrl( string $path ): ?string
	{
		$path      = wp_normalize_path( $path );
		$site_root = wp_normalize_path( ABSPATH );
		
		if ( str_starts_with( $path, $site_root ) ) {
			$relative_path = ltrim( str_replace( $site_root, '', $path ), '/' );

			$url = site_url( '/' . $relative_path );

			// Remove trailing slash if it's not root.
			return untrailingslashit( $url );
		}

		// Cannot convert if the path is outside the WP installation.
		return null;
	}

	private function capabilityAllowed( string $feature = 'all' ): bool
	{
		// Override $feature to 'all' for now.
		$feature = 'all';

		if ( ! array_key_exists( $feature, $this->allowedCapabilities ) ) {
			$feature = 'all';
		}

		$capability_required = $this->allowedCapabilities[$feature] ?? 'manage_options';

		return current_user_can( $capability_required );
	}

	private function pathAllowed( string $path ): bool
	{
		$realPath = realpath( $path ) ?: $path;

		// Normalize slashes
		$realPath = str_replace( '\\', '/', $realPath );

		return str_starts_with( $realPath, $this->realAllowedPath );
	}
}
