<?php

// True
function myFunction($arg1, $arg2=TRUE)
{
}
function myFunction($arg1, $arg2=TRUE)
{
}
function myFunction($arg1, $arg2=TRUE)
{
}

if ($variable === TRUE) { }
if ($variable === TRUE) { }
if ($variable === TRUE) { }


// False
function myFunction($arg1, $arg2=FALSE)
{
}
function myFunction($arg1, $arg2=FALSE)
{
}
function myFunction($arg1, $arg2=FALSE)
{
}

if ($variable === FALSE) { }
if ($variable === FALSE) { }
if ($variable === FALSE) { }


// Null
function myFunction($arg1, $arg2=NULL)
{
}
function myFunction($arg1, $arg2=NULL)
{
}
function myFunction($arg1, $arg2=NULL)
{
}

if ($variable === NULL) { }
if ($variable === NULL) { }
if ($variable === NULL) { }

$x = new stdClass();
$x->null = 7;

use Zend\Log\Writer\Null as NullWriter;
new \Zend\Log\Writer\Null();

namespace False;

class True extends Null implements False {}

use True\Something;
use Something\True;
class MyClass
{
    public function myFunction()
    {
        $var = array('foo' => new True());
    }
}

$x = $f?FALSE:TRUE;
$x = $f? FALSE:TRUE;

class MyClass
{
    // Spice things up a little.
    const true = FALSE;
}

var_dump(MyClass::true);

function true() {}

// Issue #3332 - ignore type declarations, but not default values.
class TypedThings {
    const MYCONST = FALSE;

    public int|false $int = FALSE;
    public Type|null $int = new MyObj(NULL);

    private function typed(int|false $param = NULL, Type|null $obj = new MyObj(FALSE)) : string|false|null
    {
        if (TRUE === FALSE) {
            return NULL;
        }
    }
}

$cl = function (int|false $param = NULL, Type|null $obj = new MyObj(FALSE)) : string|false|null {};

class SkipOverPHP82DNFTypes {
    protected (\FullyQualified&Partially\Qualified)|false $propertyC;
}

class SkipOverPHP84FinalProperties {
    final MyType|false $propA;
    private static final null|MyClass $propB;
}

// PHP 8.4 asymmetric visibility
class WithAsym {
    private(set) null|true $asym1 = TRUE;
    public private(set) ?bool $asym2 = FALSE;
    protected(set) false|string|null $asym3 = NULL;
    public protected(set) Type|null|bool $asym4 = TRUE;
}
