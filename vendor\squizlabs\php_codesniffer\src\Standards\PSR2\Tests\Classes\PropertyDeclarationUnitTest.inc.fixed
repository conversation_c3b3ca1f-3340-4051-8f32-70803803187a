<?php
class MyClass
{
    public $var = null;
    protected $var = null;
    private $var = null;
    $var = null;

    var $var = null;
    static $var = null;
    public var $var = null; // Parse error.

    public $_var = null;
    protected $_var = null;
    private $_var = null;

    public $foo, $bar, $var = null;
    public $foo,
           $bar,
           $var = null;

    protected
        $foo,
        $bar;
}

class foo
{
    const bar = <<<BAZ
qux
BAZ;
}

class ABC {
    public static $propA;
    protected static $propB;
    private static $propC;
    public static $propD;
    protected static
            $propE;
    private static    /*comment*/   $propF;
}

class MyClass
{
    public string $var = null;
    protected ?Folder\ClassName $var = null;

    var int $var = null;
    static int $var = null;

    private int $_var = null;

    public $foo, $bar, $var = null;
    public ?string $foo,
           $bar,
           $var = null;

    protected
    array $foo,
        $bar;
}

class MyClass
{
    public string $var = null;
    protected ?Folder\ClassName $var = null;
    public int $var = null;
    public static int /*comment*/$var = null;
}

class ReadOnlyProp {
    public readonly int $foo,
        $bar,
        $var = null;

    protected readonly ?string $foo;

    readonly array $foo;

    public readonly int $wrongOrder1;

    protected readonly ?string $wrongOrder2;
}

class FinalProperties {
    final public int $foo,
        $bar,
        $var = null;

    final protected (D|N)|false $foo;
    final array $foo;
    FINAL public ?int $wrongOrder1;
    final protected static ?string $wrongOrder2;
}

class AsymmetricVisibility {
    private(set) int $foo,
        $bar,
        $var = 5;

    public private(set) readonly ?string $spaces;

    protected(set) array $unfixed;

    public protected(set) int $wrongOrder1;

    protected private(set) ?string $wrongOrder2;

    final protected private(set) static bool $correctOrder;

    final protected private(set) static bool $wrongOrder3;
    final protected private(set) static bool $wrongOrder4;
    final public protected(set) static bool $wrongOrder5;
    final public public(set) static bool $wrongOrder6;

    final protected private(set) static bool $wrongOrder7;
    final protected private(set) static bool $wrongOrder8;
    final public protected(set) static bool $wrongOrder9;
    final public public(set) static bool $wrongOrder10;

    final private(set) static bool $wrongOrder11;
    final protected(set) static bool $wrongOrder12;
    final public(set) static bool $wrongOrder13;
}
