[19-Dec-2024 11:06:01 UTC] <PERSON>ron reschedule event error for hook: action_scheduler_run_queue, Error code: invalid_schedule, Error message: Event schedule does not exist., Data: {"schedule":"every_minute","args":["WP Cron"],"interval":60}
[19-Dec-2024 11:06:12 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:06:13 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:07:05 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:07:07 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:07:07 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:07:07 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:07:12 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:07:16 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:07:26 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:09:45 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:09:47 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:09:47 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:09:47 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:09:57 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:10:04 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:10:07 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:10:12 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:10:17 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:10:43 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:10:53 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:10:57 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:10:57 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:11:03 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:11:05 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:11:06 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:11:30 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:12:21 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:12:22 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:12:51 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:12:53 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:13:30 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:13:34 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:13:35 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[19-Dec-2024 11:13:42 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:22:24 UTC] Automatic updates starting...
[02-Jan-2025 02:22:30 UTC]   Automatic plugin updates starting...
[02-Jan-2025 02:22:30 UTC]     Upgrading plugin 'contact-form-7'...
[02-Jan-2025 02:22:33 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[02-Jan-2025 02:22:33 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[02-Jan-2025 02:22:33 UTC]     Plugin 'contact-form-7' has been upgraded.
[02-Jan-2025 02:22:35 UTC]     Scraping home page...
[02-Jan-2025 02:23:25 UTC] Loopback request failed: cURL error 28: Operation timed out after 50012 milliseconds with 0 bytes received
[02-Jan-2025 02:23:25 UTC]     The update for 'contact-form-7' contained a fatal error. The previously installed version has been restored.
[02-Jan-2025 02:23:25 UTC]     Upgrading plugin 'nginx-helper'...
[02-Jan-2025 02:23:28 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[02-Jan-2025 02:23:28 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[02-Jan-2025 02:23:28 UTC]     Plugin 'nginx-helper' has been upgraded.
[02-Jan-2025 02:23:30 UTC]     Scraping home page...
[02-Jan-2025 02:24:20 UTC] Loopback request failed: cURL error 28: Operation timed out after 50017 milliseconds with 0 bytes received
[02-Jan-2025 02:24:20 UTC]     The update for 'nginx-helper' contained a fatal error. The previously installed version has been restored.
[02-Jan-2025 02:24:20 UTC]   Automatic plugin updates complete.
[02-Jan-2025 02:24:21 UTC] Automatic updates complete.
[02-Jan-2025 02:25:07 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:36:06 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:36:27 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:36:28 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:36:34 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:36:48 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:36:59 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:37:40 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:38:40 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:39:40 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:40:25 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:40:28 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 02:40:28 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 02:40:45 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:40:46 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:40:46 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 02:40:46 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 02:40:59 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:40:59 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 02:40:59 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 02:42:02 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 02:42:03 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 10:22:18 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 10:22:20 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 10:22:21 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 10:22:25 UTC] Automatic updates starting...
[02-Jan-2025 10:22:29 UTC] Automatic updates complete.
[02-Jan-2025 10:25:19 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 10:25:19 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 10:25:19 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 10:25:45 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 10:25:45 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 10:25:45 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 10:26:27 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 10:26:27 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 10:26:27 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 10:26:43 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 10:26:43 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 10:26:43 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 10:27:30 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 10:27:30 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 10:27:30 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 10:27:46 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 10:27:47 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 10:27:47 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Jan-2025 10:27:48 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 10:27:58 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Jan-2025 10:28:08 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 01:46:24 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 01:46:24 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[03-Jan-2025 01:46:24 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[03-Jan-2025 01:46:28 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 01:46:29 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 01:46:37 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 01:46:38 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[03-Jan-2025 01:46:38 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[03-Jan-2025 01:46:54 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 01:46:54 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[03-Jan-2025 01:46:54 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[03-Jan-2025 01:47:56 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 01:48:57 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 01:50:58 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 01:51:59 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 01:54:00 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 01:55:46 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 01:57:44 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 01:59:45 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 02:01:46 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 02:03:47 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 02:05:48 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 03:24:37 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 03:24:40 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 03:26:36 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 03:29:39 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 03:32:02 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 03:33:14 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 09:24:05 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 09:24:08 UTC] Automatic updates starting...
[03-Jan-2025 09:24:13 UTC] Automatic updates complete.
[03-Jan-2025 09:25:28 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 09:27:29 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 09:55:46 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 09:57:47 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 09:59:48 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 10:01:49 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 10:03:50 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[03-Jan-2025 10:05:51 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[04-Jan-2025 11:43:00 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[04-Jan-2025 11:43:00 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[04-Jan-2025 11:43:01 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[04-Jan-2025 11:43:11 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[04-Jan-2025 11:45:14 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[04-Jan-2025 11:47:15 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[04-Jan-2025 11:49:16 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[04-Jan-2025 11:51:17 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[04-Jan-2025 11:53:18 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[04-Jan-2025 11:55:19 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[04-Jan-2025 11:57:20 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[04-Jan-2025 11:59:21 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[04-Jan-2025 12:00:22 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Feb-2025 10:33:52 UTC] Automatic updates starting...
[02-Feb-2025 10:33:56 UTC]   Automatic plugin updates starting...
[02-Feb-2025 10:33:56 UTC]     Upgrading plugin 'all-in-one-wp-migration'...
[02-Feb-2025 10:34:00 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[02-Feb-2025 10:34:00 UTC] PHP Stack trace:
[02-Feb-2025 10:34:00 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:34:00 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:34:00 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:34:00 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:34:00 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:34:00 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:34:00 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:34:00 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:34:00 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:34:00 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:34:00 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $id = 'w.org/plugins/all-in-one-wp-migration'; public $slug = 'all-in-one-wp-migration'; public $plugin = 'all-in-one-wp-migration/all-in-one-wp-migration.php'; public $new_version = '7.88'; public $url = 'https://wordpress.org/plugins/all-in-one-wp-migration/'; public $package = 'https://downloads.wordpress.org/plugin/all-in-one-wp-migration.7.88.zip'; public $icons = ['2x' => 'https://ps.w.org/all-in-one-wp-migration/assets/icon-256x256.png?rev=2458334', '1x' => 'https://ps.w.org/all-in-one-wp-migration/assets/icon-128x128.png?rev=2458334']; public $banners = ['2x' => 'https://ps.w.org/all-in-one-wp-migration/assets/banner-1544x500.png?rev=3209691', '1x' => 'https://ps.w.org/all-in-one-wp-migration/assets/banner-772x250.png?rev=3209691']; public $banners_rtl = []; public $requires = '3.3'; public $tested = '6.7.1'; public $requires_php = '5.3'; public $requires_plugins = []; public $current_version = '7.87' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:34:00 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'all-in-one-wp-migration/all-in-one-wp-migration.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:34:00 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://downloads.wordpress.org/plugin/all-in-one-wp-migration.7.88.zip', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'all-in-one-wp-migration/all-in-one-wp-migration.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:34:00 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/all-in-one-wp-migration.7.88/all-in-one-wp-migration/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/all-in-one-wp-migration/', 'destination_name' => 'all-in-one-wp-migration', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/all-in-one-wp-migration/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'all-in-one-wp-migration/all-in-one-wp-migration.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'all-in-one-wp-migration', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:34:00 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'all-in-one-wp-migration/all-in-one-wp-migration.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:34:00 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'all-in-one-wp-migration/all-in-one-wp-migration.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:34:00 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/all-in-one-wp-migration.7.88/all-in-one-wp-migration/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/all-in-one-wp-migration/', 'destination_name' => 'all-in-one-wp-migration', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/all-in-one-wp-migration/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'all-in-one-wp-migration/all-in-one-wp-migration.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'all-in-one-wp-migration', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:34:00 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[02-Feb-2025 10:34:00 UTC] PHP Stack trace:
[02-Feb-2025 10:34:00 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:34:00 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:34:00 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:34:00 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:34:00 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:34:00 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:34:00 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:34:00 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:34:00 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:34:00 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:34:00 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $id = 'w.org/plugins/all-in-one-wp-migration'; public $slug = 'all-in-one-wp-migration'; public $plugin = 'all-in-one-wp-migration/all-in-one-wp-migration.php'; public $new_version = '7.88'; public $url = 'https://wordpress.org/plugins/all-in-one-wp-migration/'; public $package = 'https://downloads.wordpress.org/plugin/all-in-one-wp-migration.7.88.zip'; public $icons = ['2x' => 'https://ps.w.org/all-in-one-wp-migration/assets/icon-256x256.png?rev=2458334', '1x' => 'https://ps.w.org/all-in-one-wp-migration/assets/icon-128x128.png?rev=2458334']; public $banners = ['2x' => 'https://ps.w.org/all-in-one-wp-migration/assets/banner-1544x500.png?rev=3209691', '1x' => 'https://ps.w.org/all-in-one-wp-migration/assets/banner-772x250.png?rev=3209691']; public $banners_rtl = []; public $requires = '3.3'; public $tested = '6.7.1'; public $requires_php = '5.3'; public $requires_plugins = []; public $current_version = '7.87' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:34:00 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'all-in-one-wp-migration/all-in-one-wp-migration.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:34:00 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://downloads.wordpress.org/plugin/all-in-one-wp-migration.7.88.zip', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'all-in-one-wp-migration/all-in-one-wp-migration.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:34:00 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/all-in-one-wp-migration.7.88/all-in-one-wp-migration/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/all-in-one-wp-migration/', 'destination_name' => 'all-in-one-wp-migration', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/all-in-one-wp-migration/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'all-in-one-wp-migration/all-in-one-wp-migration.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'all-in-one-wp-migration', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:34:00 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'all-in-one-wp-migration/all-in-one-wp-migration.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:34:00 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'all-in-one-wp-migration/all-in-one-wp-migration.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:34:00 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/all-in-one-wp-migration.7.88/all-in-one-wp-migration/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/all-in-one-wp-migration/', 'destination_name' => 'all-in-one-wp-migration', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/all-in-one-wp-migration/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'all-in-one-wp-migration/all-in-one-wp-migration.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'all-in-one-wp-migration', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:34:00 UTC]     Plugin 'all-in-one-wp-migration' has been upgraded.
[02-Feb-2025 10:34:02 UTC]     Scraping home page...
[02-Feb-2025 10:34:52 UTC] Loopback request failed: cURL error 28: Operation timed out after 50014 milliseconds with 0 bytes received
[02-Feb-2025 10:34:52 UTC]     The update for 'all-in-one-wp-migration' contained a fatal error. The previously installed version has been restored.
[02-Feb-2025 10:34:52 UTC]     Upgrading plugin 'contact-form-7'...
[02-Feb-2025 10:34:55 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[02-Feb-2025 10:34:55 UTC] PHP Stack trace:
[02-Feb-2025 10:34:55 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:34:55 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:34:55 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:34:55 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:34:55 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:34:55 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:34:55 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:34:55 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:34:55 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:34:55 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:34:55 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $id = 'w.org/plugins/contact-form-7'; public $slug = 'contact-form-7'; public $plugin = 'contact-form-7/wp-contact-form-7.php'; public $new_version = '6.0.3'; public $url = 'https://wordpress.org/plugins/contact-form-7/'; public $package = 'https://downloads.wordpress.org/plugin/contact-form-*******.zip'; public $icons = ['1x' => 'https://ps.w.org/contact-form-7/assets/icon.svg?rev=2339255', 'svg' => 'https://ps.w.org/contact-form-7/assets/icon.svg?rev=2339255']; public $banners = ['2x' => 'https://ps.w.org/contact-form-7/assets/banner-1544x500.png?rev=860901', '1x' => 'https://ps.w.org/contact-form-7/assets/banner-772x250.png?rev=880427']; public $banners_rtl = []; public $requires = '6.6'; public $tested = '6.7.1'; public $requires_php = '7.4'; public $requires_plugins = []; public $current_version = '6.0.2' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:34:55 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'contact-form-7/wp-contact-form-7.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:34:55 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://downloads.wordpress.org/plugin/contact-form-*******.zip', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'contact-form-7/wp-contact-form-7.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:34:55 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/contact-form-*******/contact-form-7/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/contact-form-7/', 'destination_name' => 'contact-form-7', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/contact-form-7/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'contact-form-7/wp-contact-form-7.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'contact-form-7', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:34:55 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'contact-form-7/wp-contact-form-7.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:34:55 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'contact-form-7/wp-contact-form-7.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:34:55 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/contact-form-*******/contact-form-7/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/contact-form-7/', 'destination_name' => 'contact-form-7', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/contact-form-7/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'contact-form-7/wp-contact-form-7.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'contact-form-7', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:34:55 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[02-Feb-2025 10:34:55 UTC] PHP Stack trace:
[02-Feb-2025 10:34:55 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:34:55 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:34:55 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:34:55 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:34:55 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:34:55 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:34:55 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:34:55 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:34:55 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:34:55 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:34:55 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $id = 'w.org/plugins/contact-form-7'; public $slug = 'contact-form-7'; public $plugin = 'contact-form-7/wp-contact-form-7.php'; public $new_version = '6.0.3'; public $url = 'https://wordpress.org/plugins/contact-form-7/'; public $package = 'https://downloads.wordpress.org/plugin/contact-form-*******.zip'; public $icons = ['1x' => 'https://ps.w.org/contact-form-7/assets/icon.svg?rev=2339255', 'svg' => 'https://ps.w.org/contact-form-7/assets/icon.svg?rev=2339255']; public $banners = ['2x' => 'https://ps.w.org/contact-form-7/assets/banner-1544x500.png?rev=860901', '1x' => 'https://ps.w.org/contact-form-7/assets/banner-772x250.png?rev=880427']; public $banners_rtl = []; public $requires = '6.6'; public $tested = '6.7.1'; public $requires_php = '7.4'; public $requires_plugins = []; public $current_version = '6.0.2' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:34:55 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'contact-form-7/wp-contact-form-7.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:34:55 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://downloads.wordpress.org/plugin/contact-form-*******.zip', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'contact-form-7/wp-contact-form-7.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:34:55 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/contact-form-*******/contact-form-7/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/contact-form-7/', 'destination_name' => 'contact-form-7', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/contact-form-7/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'contact-form-7/wp-contact-form-7.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'contact-form-7', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:34:55 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'contact-form-7/wp-contact-form-7.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:34:55 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'contact-form-7/wp-contact-form-7.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:34:55 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/contact-form-*******/contact-form-7/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/contact-form-7/', 'destination_name' => 'contact-form-7', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/contact-form-7/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'contact-form-7/wp-contact-form-7.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'contact-form-7', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:34:55 UTC]     Plugin 'contact-form-7' has been upgraded.
[02-Feb-2025 10:34:57 UTC]     Scraping home page...
[02-Feb-2025 10:35:47 UTC] Loopback request failed: cURL error 28: Operation timed out after 50009 milliseconds with 0 bytes received
[02-Feb-2025 10:35:47 UTC]     The update for 'contact-form-7' contained a fatal error. The previously installed version has been restored.
[02-Feb-2025 10:35:47 UTC]     Upgrading plugin 'nginx-helper'...
[02-Feb-2025 10:35:50 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[02-Feb-2025 10:35:50 UTC] PHP Stack trace:
[02-Feb-2025 10:35:50 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:35:50 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:35:50 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:35:50 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:35:50 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:35:50 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:35:50 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:35:50 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:35:50 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:35:50 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:35:50 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $id = 'w.org/plugins/nginx-helper'; public $slug = 'nginx-helper'; public $plugin = 'nginx-helper/nginx-helper.php'; public $new_version = '2.3.2'; public $url = 'https://wordpress.org/plugins/nginx-helper/'; public $package = 'https://downloads.wordpress.org/plugin/nginx-helper.2.3.2.zip'; public $icons = ['1x' => 'https://ps.w.org/nginx-helper/assets/icon.svg?rev=2360932', 'svg' => 'https://ps.w.org/nginx-helper/assets/icon.svg?rev=2360932']; public $banners = ['2x' => 'https://ps.w.org/nginx-helper/assets/banner-1544x500.png?rev=2360932', '1x' => 'https://ps.w.org/nginx-helper/assets/banner-772x250.png?rev=2360926']; public $banners_rtl = ['2x' => 'https://ps.w.org/nginx-helper/assets/banner-1544x500-rtl.png?rev=2360932', '1x' => 'https://ps.w.org/nginx-helper/assets/banner-772x250-rtl.png?rev=2360932']; public $requires = '3.0'; public $tested = '6.7.1'; public $requires_php = FALSE; public $requires_plugins = []; public $current_version = '2.3.1' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:35:50 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'nginx-helper/nginx-helper.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:35:50 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://downloads.wordpress.org/plugin/nginx-helper.2.3.2.zip', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'nginx-helper/nginx-helper.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:35:50 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/nginx-helper.2.3.2/nginx-helper/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/nginx-helper/', 'destination_name' => 'nginx-helper', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/nginx-helper/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'nginx-helper/nginx-helper.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'nginx-helper', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:35:50 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'nginx-helper/nginx-helper.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:35:50 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'nginx-helper/nginx-helper.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:35:50 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/nginx-helper.2.3.2/nginx-helper/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/nginx-helper/', 'destination_name' => 'nginx-helper', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/nginx-helper/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'nginx-helper/nginx-helper.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'nginx-helper', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:35:50 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[02-Feb-2025 10:35:50 UTC] PHP Stack trace:
[02-Feb-2025 10:35:50 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:35:50 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:35:50 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:35:50 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:35:50 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:35:50 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:35:50 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:35:50 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:35:50 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:35:50 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:35:50 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $id = 'w.org/plugins/nginx-helper'; public $slug = 'nginx-helper'; public $plugin = 'nginx-helper/nginx-helper.php'; public $new_version = '2.3.2'; public $url = 'https://wordpress.org/plugins/nginx-helper/'; public $package = 'https://downloads.wordpress.org/plugin/nginx-helper.2.3.2.zip'; public $icons = ['1x' => 'https://ps.w.org/nginx-helper/assets/icon.svg?rev=2360932', 'svg' => 'https://ps.w.org/nginx-helper/assets/icon.svg?rev=2360932']; public $banners = ['2x' => 'https://ps.w.org/nginx-helper/assets/banner-1544x500.png?rev=2360932', '1x' => 'https://ps.w.org/nginx-helper/assets/banner-772x250.png?rev=2360926']; public $banners_rtl = ['2x' => 'https://ps.w.org/nginx-helper/assets/banner-1544x500-rtl.png?rev=2360932', '1x' => 'https://ps.w.org/nginx-helper/assets/banner-772x250-rtl.png?rev=2360932']; public $requires = '3.0'; public $tested = '6.7.1'; public $requires_php = FALSE; public $requires_plugins = []; public $current_version = '2.3.1' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:35:50 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'nginx-helper/nginx-helper.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:35:50 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://downloads.wordpress.org/plugin/nginx-helper.2.3.2.zip', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'nginx-helper/nginx-helper.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:35:50 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/nginx-helper.2.3.2/nginx-helper/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/nginx-helper/', 'destination_name' => 'nginx-helper', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/nginx-helper/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'nginx-helper/nginx-helper.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'nginx-helper', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:35:50 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'nginx-helper/nginx-helper.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:35:50 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'nginx-helper/nginx-helper.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:35:50 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/nginx-helper.2.3.2/nginx-helper/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/nginx-helper/', 'destination_name' => 'nginx-helper', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/nginx-helper/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'nginx-helper/nginx-helper.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'nginx-helper', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:35:50 UTC]     Plugin 'nginx-helper' has been upgraded.
[02-Feb-2025 10:35:50 UTC]     'nginx-helper/nginx-helper.php' is inactive and will not be checked for fatal errors.
[02-Feb-2025 10:35:50 UTC]     Upgrading plugin 'post-duplicator'...
[02-Feb-2025 10:35:52 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[02-Feb-2025 10:35:52 UTC] PHP Stack trace:
[02-Feb-2025 10:35:52 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:35:52 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:35:52 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:35:52 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:35:52 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:35:52 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:35:52 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:35:52 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:35:52 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:35:52 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:35:52 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $id = 'w.org/plugins/post-duplicator'; public $slug = 'post-duplicator'; public $plugin = 'post-duplicator/m4c-postduplicator.php'; public $new_version = '2.37'; public $url = 'https://wordpress.org/plugins/post-duplicator/'; public $package = 'https://downloads.wordpress.org/plugin/post-duplicator.2.37.zip'; public $icons = ['1x' => 'https://ps.w.org/post-duplicator/assets/icon-128x128.png?rev=1587588']; public $banners = ['2x' => 'https://ps.w.org/post-duplicator/assets/banner-1544x500.png?rev=1587588', '1x' => 'https://ps.w.org/post-duplicator/assets/banner-772x250.png?rev=1587588']; public $banners_rtl = []; public $requires = '4.0'; public $tested = '6.6.2'; public $requires_php = FALSE; public $requires_plugins = []; public $current_version = '2.36' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:35:52 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'post-duplicator/m4c-postduplicator.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:35:52 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://downloads.wordpress.org/plugin/post-duplicator.2.37.zip', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'post-duplicator/m4c-postduplicator.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:35:52 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/post-duplicator.2.37/post-duplicator/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/post-duplicator/', 'destination_name' => 'post-duplicator', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/post-duplicator/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'post-duplicator/m4c-postduplicator.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'post-duplicator', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:35:52 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'post-duplicator/m4c-postduplicator.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:35:52 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'post-duplicator/m4c-postduplicator.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:35:52 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/post-duplicator.2.37/post-duplicator/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/post-duplicator/', 'destination_name' => 'post-duplicator', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/post-duplicator/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'post-duplicator/m4c-postduplicator.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'post-duplicator', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:35:52 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[02-Feb-2025 10:35:52 UTC] PHP Stack trace:
[02-Feb-2025 10:35:52 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:35:52 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:35:52 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:35:52 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:35:52 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:35:52 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:35:52 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:35:52 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:35:52 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:35:52 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:35:52 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $id = 'w.org/plugins/post-duplicator'; public $slug = 'post-duplicator'; public $plugin = 'post-duplicator/m4c-postduplicator.php'; public $new_version = '2.37'; public $url = 'https://wordpress.org/plugins/post-duplicator/'; public $package = 'https://downloads.wordpress.org/plugin/post-duplicator.2.37.zip'; public $icons = ['1x' => 'https://ps.w.org/post-duplicator/assets/icon-128x128.png?rev=1587588']; public $banners = ['2x' => 'https://ps.w.org/post-duplicator/assets/banner-1544x500.png?rev=1587588', '1x' => 'https://ps.w.org/post-duplicator/assets/banner-772x250.png?rev=1587588']; public $banners_rtl = []; public $requires = '4.0'; public $tested = '6.6.2'; public $requires_php = FALSE; public $requires_plugins = []; public $current_version = '2.36' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:35:52 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'post-duplicator/m4c-postduplicator.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:35:52 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://downloads.wordpress.org/plugin/post-duplicator.2.37.zip', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'post-duplicator/m4c-postduplicator.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:35:52 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/post-duplicator.2.37/post-duplicator/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/post-duplicator/', 'destination_name' => 'post-duplicator', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/post-duplicator/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'post-duplicator/m4c-postduplicator.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'post-duplicator', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:35:52 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'post-duplicator/m4c-postduplicator.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:35:52 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'post-duplicator/m4c-postduplicator.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:35:52 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/post-duplicator.2.37/post-duplicator/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/post-duplicator/', 'destination_name' => 'post-duplicator', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/post-duplicator/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'post-duplicator/m4c-postduplicator.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'post-duplicator', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:35:52 UTC]     Plugin 'post-duplicator' has been upgraded.
[02-Feb-2025 10:35:54 UTC]     Scraping home page...
[02-Feb-2025 10:36:44 UTC] Loopback request failed: cURL error 28: Operation timed out after 50006 milliseconds with 0 bytes received
[02-Feb-2025 10:36:44 UTC]     The update for 'post-duplicator' contained a fatal error. The previously installed version has been restored.
[02-Feb-2025 10:36:44 UTC]     Upgrading plugin 'really-simple-captcha'...
[02-Feb-2025 10:36:47 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[02-Feb-2025 10:36:47 UTC] PHP Stack trace:
[02-Feb-2025 10:36:47 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:36:47 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:36:47 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:36:47 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:36:47 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:36:47 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:36:47 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:36:47 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:36:47 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:36:47 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:36:47 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $id = 'w.org/plugins/really-simple-captcha'; public $slug = 'really-simple-captcha'; public $plugin = 'really-simple-captcha/really-simple-captcha.php'; public $new_version = '2.4'; public $url = 'https://wordpress.org/plugins/really-simple-captcha/'; public $package = 'https://downloads.wordpress.org/plugin/really-simple-captcha.2.4.zip'; public $icons = ['2x' => 'https://ps.w.org/really-simple-captcha/assets/icon-256x256.png?rev=1047241', '1x' => 'https://ps.w.org/really-simple-captcha/assets/icon-128x128.png?rev=1047241']; public $banners = ['2x' => 'https://ps.w.org/really-simple-captcha/assets/banner-1544x500.png?rev=880406', '1x' => 'https://ps.w.org/really-simple-captcha/assets/banner-772x250.png?rev=880406']; public $banners_rtl = []; public $requires = '6.6'; public $tested = '6.7.1'; public $requires_php = '7.4'; public $requires_plugins = []; public $current_version = '2.3' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:36:47 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'really-simple-captcha/really-simple-captcha.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:36:47 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://downloads.wordpress.org/plugin/really-simple-captcha.2.4.zip', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'really-simple-captcha/really-simple-captcha.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:36:47 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/really-simple-captcha.2.4/really-simple-captcha/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/really-simple-captcha/', 'destination_name' => 'really-simple-captcha', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/really-simple-captcha/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'really-simple-captcha/really-simple-captcha.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'really-simple-captcha', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:36:47 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'really-simple-captcha/really-simple-captcha.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:36:47 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'really-simple-captcha/really-simple-captcha.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:36:47 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/really-simple-captcha.2.4/really-simple-captcha/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/really-simple-captcha/', 'destination_name' => 'really-simple-captcha', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/really-simple-captcha/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'really-simple-captcha/really-simple-captcha.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'really-simple-captcha', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:36:47 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[02-Feb-2025 10:36:47 UTC] PHP Stack trace:
[02-Feb-2025 10:36:47 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:36:47 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:36:47 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:36:47 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:36:47 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:36:47 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:36:47 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:36:47 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:36:47 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:36:47 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:36:47 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $id = 'w.org/plugins/really-simple-captcha'; public $slug = 'really-simple-captcha'; public $plugin = 'really-simple-captcha/really-simple-captcha.php'; public $new_version = '2.4'; public $url = 'https://wordpress.org/plugins/really-simple-captcha/'; public $package = 'https://downloads.wordpress.org/plugin/really-simple-captcha.2.4.zip'; public $icons = ['2x' => 'https://ps.w.org/really-simple-captcha/assets/icon-256x256.png?rev=1047241', '1x' => 'https://ps.w.org/really-simple-captcha/assets/icon-128x128.png?rev=1047241']; public $banners = ['2x' => 'https://ps.w.org/really-simple-captcha/assets/banner-1544x500.png?rev=880406', '1x' => 'https://ps.w.org/really-simple-captcha/assets/banner-772x250.png?rev=880406']; public $banners_rtl = []; public $requires = '6.6'; public $tested = '6.7.1'; public $requires_php = '7.4'; public $requires_plugins = []; public $current_version = '2.3' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:36:47 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'really-simple-captcha/really-simple-captcha.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:36:47 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://downloads.wordpress.org/plugin/really-simple-captcha.2.4.zip', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'really-simple-captcha/really-simple-captcha.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:36:47 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/really-simple-captcha.2.4/really-simple-captcha/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/really-simple-captcha/', 'destination_name' => 'really-simple-captcha', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/really-simple-captcha/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'really-simple-captcha/really-simple-captcha.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'really-simple-captcha', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:36:47 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'really-simple-captcha/really-simple-captcha.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:36:47 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'really-simple-captcha/really-simple-captcha.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:36:47 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/really-simple-captcha.2.4/really-simple-captcha/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/really-simple-captcha/', 'destination_name' => 'really-simple-captcha', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/really-simple-captcha/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'really-simple-captcha/really-simple-captcha.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'really-simple-captcha', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:36:47 UTC]     Plugin 'really-simple-captcha' has been upgraded.
[02-Feb-2025 10:36:49 UTC]     Scraping home page...
[02-Feb-2025 10:37:39 UTC] Loopback request failed: cURL error 28: Operation timed out after 50015 milliseconds with 0 bytes received
[02-Feb-2025 10:37:39 UTC]     The update for 'really-simple-captcha' contained a fatal error. The previously installed version has been restored.
[02-Feb-2025 10:37:39 UTC]     Upgrading plugin 'shortpixel-image-optimiser'...
[02-Feb-2025 10:37:44 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[02-Feb-2025 10:37:44 UTC] PHP Stack trace:
[02-Feb-2025 10:37:44 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:37:44 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:37:44 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:37:44 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:37:44 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:37:44 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:37:44 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:37:44 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:37:44 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:37:44 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:37:44 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $id = 'w.org/plugins/shortpixel-image-optimiser'; public $slug = 'shortpixel-image-optimiser'; public $plugin = 'shortpixel-image-optimiser/wp-shortpixel.php'; public $new_version = '6.1.1'; public $url = 'https://wordpress.org/plugins/shortpixel-image-optimiser/'; public $package = 'https://downloads.wordpress.org/plugin/shortpixel-image-optimiser.6.1.1.zip'; public $icons = ['2x' => 'https://ps.w.org/shortpixel-image-optimiser/assets/icon-256x256.png?rev=1038819', '1x' => 'https://ps.w.org/shortpixel-image-optimiser/assets/icon-128x128.png?rev=1038819']; public $banners = ['2x' => 'https://ps.w.org/shortpixel-image-optimiser/assets/banner-1544x500.png?rev=3228266', '1x' => 'https://ps.w.org/shortpixel-image-optimiser/assets/banner-772x250.png?rev=3228266']; public $banners_rtl = []; public $requires = '4.8.0'; public $tested = '6.7.1'; public $requires_php = '7.4'; public $requires_plugins = []; public $current_version = '6.0.4' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:37:44 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'shortpixel-image-optimiser/wp-shortpixel.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:37:44 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://downloads.wordpress.org/plugin/shortpixel-image-optimiser.6.1.1.zip', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'shortpixel-image-optimiser/wp-shortpixel.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:37:44 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/shortpixel-image-optimiser.6.1.1/shortpixel-image-optimiser/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/shortpixel-image-optimiser/', 'destination_name' => 'shortpixel-image-optimiser', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/shortpixel-image-optimiser/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'shortpixel-image-optimiser/wp-shortpixel.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'shortpixel-image-optimiser', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:37:44 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'shortpixel-image-optimiser/wp-shortpixel.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:37:44 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'shortpixel-image-optimiser/wp-shortpixel.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:37:44 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/shortpixel-image-optimiser.6.1.1/shortpixel-image-optimiser/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/shortpixel-image-optimiser/', 'destination_name' => 'shortpixel-image-optimiser', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/shortpixel-image-optimiser/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'shortpixel-image-optimiser/wp-shortpixel.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'shortpixel-image-optimiser', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:37:44 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[02-Feb-2025 10:37:44 UTC] PHP Stack trace:
[02-Feb-2025 10:37:44 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:37:44 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:37:44 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:37:44 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:37:44 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:37:44 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:37:44 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:37:44 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:37:44 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:37:44 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:37:44 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $id = 'w.org/plugins/shortpixel-image-optimiser'; public $slug = 'shortpixel-image-optimiser'; public $plugin = 'shortpixel-image-optimiser/wp-shortpixel.php'; public $new_version = '6.1.1'; public $url = 'https://wordpress.org/plugins/shortpixel-image-optimiser/'; public $package = 'https://downloads.wordpress.org/plugin/shortpixel-image-optimiser.6.1.1.zip'; public $icons = ['2x' => 'https://ps.w.org/shortpixel-image-optimiser/assets/icon-256x256.png?rev=1038819', '1x' => 'https://ps.w.org/shortpixel-image-optimiser/assets/icon-128x128.png?rev=1038819']; public $banners = ['2x' => 'https://ps.w.org/shortpixel-image-optimiser/assets/banner-1544x500.png?rev=3228266', '1x' => 'https://ps.w.org/shortpixel-image-optimiser/assets/banner-772x250.png?rev=3228266']; public $banners_rtl = []; public $requires = '4.8.0'; public $tested = '6.7.1'; public $requires_php = '7.4'; public $requires_plugins = []; public $current_version = '6.0.4' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:37:44 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'shortpixel-image-optimiser/wp-shortpixel.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:37:44 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://downloads.wordpress.org/plugin/shortpixel-image-optimiser.6.1.1.zip', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'shortpixel-image-optimiser/wp-shortpixel.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:37:44 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/shortpixel-image-optimiser.6.1.1/shortpixel-image-optimiser/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/shortpixel-image-optimiser/', 'destination_name' => 'shortpixel-image-optimiser', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/shortpixel-image-optimiser/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'shortpixel-image-optimiser/wp-shortpixel.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'shortpixel-image-optimiser', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:37:44 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'shortpixel-image-optimiser/wp-shortpixel.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:37:44 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'shortpixel-image-optimiser/wp-shortpixel.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:37:44 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/shortpixel-image-optimiser.6.1.1/shortpixel-image-optimiser/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/shortpixel-image-optimiser/', 'destination_name' => 'shortpixel-image-optimiser', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/shortpixel-image-optimiser/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'shortpixel-image-optimiser/wp-shortpixel.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'shortpixel-image-optimiser', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:37:44 UTC]     Plugin 'shortpixel-image-optimiser' has been upgraded.
[02-Feb-2025 10:37:44 UTC]     'shortpixel-image-optimiser/wp-shortpixel.php' is inactive and will not be checked for fatal errors.
[02-Feb-2025 10:37:44 UTC]     Upgrading plugin 'wordpress-seo'...
[02-Feb-2025 10:37:52 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[02-Feb-2025 10:37:52 UTC] PHP Stack trace:
[02-Feb-2025 10:37:52 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:37:52 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:37:52 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:37:52 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:37:52 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:37:52 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:37:52 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:37:52 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:37:52 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:37:52 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:37:52 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $id = 'w.org/plugins/wordpress-seo'; public $slug = 'wordpress-seo'; public $plugin = 'wordpress-seo/wp-seo.php'; public $new_version = '24.3'; public $url = 'https://wordpress.org/plugins/wordpress-seo/'; public $package = 'https://downloads.wordpress.org/plugin/wordpress-seo.24.3.zip'; public $icons = ['2x' => 'https://ps.w.org/wordpress-seo/assets/icon-256x256.gif?rev=3112542', '1x' => 'https://ps.w.org/wordpress-seo/assets/icon-128x128.gif?rev=3112542']; public $banners = ['2x' => 'https://ps.w.org/wordpress-seo/assets/banner-1544x500.png?rev=2643727', '1x' => 'https://ps.w.org/wordpress-seo/assets/banner-772x250.png?rev=2643727']; public $banners_rtl = ['2x' => 'https://ps.w.org/wordpress-seo/assets/banner-1544x500-rtl.png?rev=2643727', '1x' => 'https://ps.w.org/wordpress-seo/assets/banner-772x250-rtl.png?rev=2643727']; public $requires = '6.5'; public $tested = '6.7.1'; public $requires_php = '7.2.5'; public $requires_plugins = []; public $current_version = '24.1' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:37:52 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'wordpress-seo/wp-seo.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:37:52 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://downloads.wordpress.org/plugin/wordpress-seo.24.3.zip', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'wordpress-seo/wp-seo.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:37:52 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/wordpress-seo.24.3/wordpress-seo/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/wordpress-seo/', 'destination_name' => 'wordpress-seo', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/wordpress-seo/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'wordpress-seo/wp-seo.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'wordpress-seo', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:37:52 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'wordpress-seo/wp-seo.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:37:52 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'wordpress-seo/wp-seo.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:37:52 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/wordpress-seo.24.3/wordpress-seo/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/wordpress-seo/', 'destination_name' => 'wordpress-seo', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/wordpress-seo/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'wordpress-seo/wp-seo.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'wordpress-seo', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:37:52 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[02-Feb-2025 10:37:52 UTC] PHP Stack trace:
[02-Feb-2025 10:37:52 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:37:52 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:37:52 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:37:52 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:37:52 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:37:52 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:37:52 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:37:52 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:37:52 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:37:52 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:37:52 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $id = 'w.org/plugins/wordpress-seo'; public $slug = 'wordpress-seo'; public $plugin = 'wordpress-seo/wp-seo.php'; public $new_version = '24.3'; public $url = 'https://wordpress.org/plugins/wordpress-seo/'; public $package = 'https://downloads.wordpress.org/plugin/wordpress-seo.24.3.zip'; public $icons = ['2x' => 'https://ps.w.org/wordpress-seo/assets/icon-256x256.gif?rev=3112542', '1x' => 'https://ps.w.org/wordpress-seo/assets/icon-128x128.gif?rev=3112542']; public $banners = ['2x' => 'https://ps.w.org/wordpress-seo/assets/banner-1544x500.png?rev=2643727', '1x' => 'https://ps.w.org/wordpress-seo/assets/banner-772x250.png?rev=2643727']; public $banners_rtl = ['2x' => 'https://ps.w.org/wordpress-seo/assets/banner-1544x500-rtl.png?rev=2643727', '1x' => 'https://ps.w.org/wordpress-seo/assets/banner-772x250-rtl.png?rev=2643727']; public $requires = '6.5'; public $tested = '6.7.1'; public $requires_php = '7.2.5'; public $requires_plugins = []; public $current_version = '24.1' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:37:52 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'wordpress-seo/wp-seo.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:37:52 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://downloads.wordpress.org/plugin/wordpress-seo.24.3.zip', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'wordpress-seo/wp-seo.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:37:52 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/wordpress-seo.24.3/wordpress-seo/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/wordpress-seo/', 'destination_name' => 'wordpress-seo', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/wordpress-seo/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'wordpress-seo/wp-seo.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'wordpress-seo', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:37:52 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'wordpress-seo/wp-seo.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:37:52 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'wordpress-seo/wp-seo.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:37:52 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/wordpress-seo.24.3/wordpress-seo/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/wordpress-seo/', 'destination_name' => 'wordpress-seo', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/wordpress-seo/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'wordpress-seo/wp-seo.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'wordpress-seo', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:37:52 UTC]     Plugin 'wordpress-seo' has been upgraded.
[02-Feb-2025 10:37:54 UTC]     Scraping home page...
[02-Feb-2025 10:38:49 UTC] Loopback request failed: cURL error 28: Operation timed out after 50011 milliseconds with 0 bytes received
[02-Feb-2025 10:38:50 UTC]     The update for 'wordpress-seo' contained a fatal error. The previously installed version has been restored.
[02-Feb-2025 10:38:50 UTC]     Upgrading plugin 'advanced-custom-fields-pro'...
[02-Feb-2025 10:38:57 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[02-Feb-2025 10:38:57 UTC] PHP Stack trace:
[02-Feb-2025 10:38:57 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:38:57 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:38:57 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:38:57 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:38:57 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:38:57 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:38:57 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:38:57 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:38:57 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:38:57 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:38:57 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $slug = 'advanced-custom-fields-pro'; public $plugin = 'advanced-custom-fields-pro/acf.php'; public $new_version = '6.3.12'; public $url = 'https://www.advancedcustomfields.com'; public $tested = '6.7.1'; public $package = 'https://connect.advancedcustomfields.com/v2/plugins/download?p=pro&s=plugin&version=6.3.12&token=eyJwIjoicHJvIiwiayI6ImIzSmtaWEpmYVdROU1UYzRPRGM1ZkhSNWNHVTlaR1YyWld4dmNHVnlmR1JoZEdVOU1qQXhPUzB4TWkwd01pQXdNam96TnpvME1nPT0iLCJ3cF91cmwiOiJodHRwOlwvXC9zdWFyd29vZHRhYmxlLmxvY2FsIiwid3BfdmVyc2lvbiI6IjYuNy4xIiwid3BfbXVsdGlzaXRlIjowLCJwaHBfdmVyc2lvbiI6IjguMy4xNCIsImJsb2NrX2NvdW50IjowfQ=='; public $icons = ['default' => 'https://connect.advancedcustomfields.com/assets/icon-256x256.png']; public $banners = ['low' => 'https://connect.advancedcustomfields.com/assets/banner-772x250.jpg', 'high' => 'https://connect.advancedcustomfields.com/assets/banner-1544x500.jpg']; public $requires = '6.0'; public $requires_php = '7.4'; public $release_date = '20250121'; public $license_valid = TRUE; public $current_version = '6.3.11' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:38:57 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'advanced-custom-fields-pro/acf.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:38:57 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://connect.advancedcustomfields.com/v2/plugins/download?p=pro&s=plugin&version=6.3.12&token=eyJwIjoicHJvIiwiayI6ImIzSmtaWEpmYVdROU1UYzRPRGM1ZkhSNWNHVTlaR1YyWld4dmNHVnlmR1JoZEdVOU1qQXhPUzB4TWkwd01pQXdNam96TnpvME1nPT0iLCJ3cF91cmwiOiJodHRwOlwvXC9zdWFyd29vZHRhYmxlLmxvY2FsIiwid3BfdmVyc2lvbiI6IjYuNy4xIiwid3BfbXVsdGlzaXRlIjowLCJwaHBfdmVyc2lvbiI6IjguMy4xNCIsImJsb2NrX2NvdW50IjowfQ==', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'advanced-custom-fields-pro/acf.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:38:57 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/advanced-custom-fields-pro/advanced-custom-fields-pro/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/advanced-custom-fields-pro/', 'destination_name' => 'advanced-custom-fields-pro', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/advanced-custom-fields-pro/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'advanced-custom-fields-pro/acf.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'advanced-custom-fields-pro', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:38:57 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'advanced-custom-fields-pro/acf.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:38:57 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'advanced-custom-fields-pro/acf.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:38:57 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/advanced-custom-fields-pro/advanced-custom-fields-pro/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/advanced-custom-fields-pro/', 'destination_name' => 'advanced-custom-fields-pro', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/advanced-custom-fields-pro/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'advanced-custom-fields-pro/acf.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'advanced-custom-fields-pro', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:38:57 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[02-Feb-2025 10:38:57 UTC] PHP Stack trace:
[02-Feb-2025 10:38:57 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-cron.php:0
[02-Feb-2025 10:38:57 UTC] PHP   2. do_action_ref_array($hook_name = 'wp_version_check', $args = []) D:\www\suarwoodtable\wp-cron.php:191
[02-Feb-2025 10:38:57 UTC] PHP   3. WP_Hook->do_action($args = []) D:\www\suarwoodtable\wp-includes\plugin.php:565
[02-Feb-2025 10:38:57 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:38:57 UTC] PHP   5. wp_version_check($extra_stats = *uninitialized*, $force_check = *uninitialized*) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:38:57 UTC] PHP   6. do_action($hook_name = 'wp_maybe_auto_update') D:\www\suarwoodtable\wp-includes\update.php:295
[02-Feb-2025 10:38:57 UTC] PHP   7. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:38:57 UTC] PHP   8. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:38:57 UTC] PHP   9. wp_maybe_auto_update('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:38:57 UTC] PHP  10. WP_Automatic_Updater->run() D:\www\suarwoodtable\wp-includes\update.php:851
[02-Feb-2025 10:38:57 UTC] PHP  11. WP_Automatic_Updater->update($type = 'plugin', $item = class stdClass { public $slug = 'advanced-custom-fields-pro'; public $plugin = 'advanced-custom-fields-pro/acf.php'; public $new_version = '6.3.12'; public $url = 'https://www.advancedcustomfields.com'; public $tested = '6.7.1'; public $package = 'https://connect.advancedcustomfields.com/v2/plugins/download?p=pro&s=plugin&version=6.3.12&token=eyJwIjoicHJvIiwiayI6ImIzSmtaWEpmYVdROU1UYzRPRGM1ZkhSNWNHVTlaR1YyWld4dmNHVnlmR1JoZEdVOU1qQXhPUzB4TWkwd01pQXdNam96TnpvME1nPT0iLCJ3cF91cmwiOiJodHRwOlwvXC9zdWFyd29vZHRhYmxlLmxvY2FsIiwid3BfdmVyc2lvbiI6IjYuNy4xIiwid3BfbXVsdGlzaXRlIjowLCJwaHBfdmVyc2lvbiI6IjguMy4xNCIsImJsb2NrX2NvdW50IjowfQ=='; public $icons = ['default' => 'https://connect.advancedcustomfields.com/assets/icon-256x256.png']; public $banners = ['low' => 'https://connect.advancedcustomfields.com/assets/banner-772x250.jpg', 'high' => 'https://connect.advancedcustomfields.com/assets/banner-1544x500.jpg']; public $requires = '6.0'; public $requires_php = '7.4'; public $release_date = '20250121'; public $license_valid = TRUE; public $current_version = '6.3.11' }) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:676
[02-Feb-2025 10:38:57 UTC] PHP  12. Plugin_Upgrader->upgrade($plugin = 'advanced-custom-fields-pro/acf.php', $args = ['clear_update_cache' => FALSE, 'pre_check_md5' => FALSE, 'attempt_rollback' => TRUE, 'allow_relaxed_file_ownership' => FALSE]) D:\www\suarwoodtable\wp-admin\includes\class-wp-automatic-updater.php:478
[02-Feb-2025 10:38:57 UTC] PHP  13. WP_Upgrader->run($options = ['package' => 'https://connect.advancedcustomfields.com/v2/plugins/download?p=pro&s=plugin&version=6.3.12&token=eyJwIjoicHJvIiwiayI6ImIzSmtaWEpmYVdROU1UYzRPRGM1ZkhSNWNHVTlaR1YyWld4dmNHVnlmR1JoZEdVOU1qQXhPUzB4TWkwd01pQXdNam96TnpvME1nPT0iLCJ3cF91cmwiOiJodHRwOlwvXC9zdWFyd29vZHRhYmxlLmxvY2FsIiwid3BfdmVyc2lvbiI6IjYuNy4xIiwid3BfbXVsdGlzaXRlIjowLCJwaHBfdmVyc2lvbiI6IjguMy4xNCIsImJsb2NrX2NvdW50IjowfQ==', 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'clear_destination' => TRUE, 'clear_working' => TRUE, 'hook_extra' => ['plugin' => 'advanced-custom-fields-pro/acf.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-admin\includes\class-plugin-upgrader.php:224
[02-Feb-2025 10:38:57 UTC] PHP  14. do_action($hook_name = 'upgrader_process_complete', ...$arg = variadic(class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/advanced-custom-fields-pro/advanced-custom-fields-pro/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/advanced-custom-fields-pro/', 'destination_name' => 'advanced-custom-fields-pro', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/advanced-custom-fields-pro/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, ['plugin' => 'advanced-custom-fields-pro/acf.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'advanced-custom-fields-pro', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']])) D:\www\suarwoodtable\wp-admin\includes\class-wp-upgrader.php:984
[02-Feb-2025 10:38:57 UTC] PHP  15. WP_Hook->do_action($args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'advanced-custom-fields-pro/acf.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:38:57 UTC] PHP  16. WP_Hook->apply_filters($value = '', $args = [0 => class Plugin_Upgrader { public $strings = [...]; public $skin = class Automatic_Upgrader_Skin { ... }; public $result = [...]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [...]; private ${WP_Upgrader}temp_restores = [...]; public $bulk = FALSE; public $new_plugin_data = [...] }, 1 => ['plugin' => 'advanced-custom-fields-pro/acf.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => [...]]]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:38:57 UTC] PHP  17. vc_set_promo_editor_popup($upgrade_object = class Plugin_Upgrader { public $strings = ['bad_request' => 'Invalid data provided.', 'fs_unavailable' => 'Could not access filesystem.', 'fs_error' => 'Filesystem error.', 'fs_no_root_dir' => 'Unable to locate WordPress root directory.', 'fs_no_content_dir' => 'Unable to locate WordPress content directory (wp-content).', 'fs_no_plugins_dir' => 'Unable to locate WordPress plugin directory.', 'fs_no_themes_dir' => 'Unable to locate WordPress theme directory.', 'fs_no_folder' => 'Unable to locate needed folder (%s).', 'no_package' => 'Update package not available.', 'download_failed' => 'Download failed.', 'installing_package' => 'Installing the latest version&#8230;', 'no_files' => 'The package contains no files.', 'folder_exists' => 'Destination folder already exists.', 'mkdir_failed' => 'Could not create directory.', 'incompatible_archive' => 'The package could not be installed.', 'files_not_writable' => 'The update cannot be installed because some files could not be copied. This is usually due to inconsistent file permissions.', 'dir_not_readable' => 'A directory could not be read.', 'maintenance_start' => 'Enabling Maintenance mode&#8230;', 'maintenance_end' => 'Disabling Maintenance mode&#8230;', 'temp_backup_mkdir_failed' => 'Could not create the upgrade-temp-backup directory.', 'temp_backup_move_failed' => 'Could not move the old version to the upgrade-temp-backup directory.', 'temp_backup_restore_failed' => 'Could not restore the original version of %s.', 'temp_backup_delete_failed' => 'Could not delete the temporary backup directory for %s.', 'up_to_date' => 'The plugin is at the latest version.', 'downloading_package' => 'Downloading update from <span class="code pre">%s</span>&#8230;', 'unpack_package' => 'Unpacking the update&#8230;', 'remove_old' => 'Removing the old version of the plugin&#8230;', 'remove_old_failed' => 'Could not remove the old plugin.', 'process_failed' => 'Plugin update failed.', 'process_success' => 'Plugin updated successfully.', 'process_bulk_success' => 'Plugins updated successfully.']; public $skin = class Automatic_Upgrader_Skin { public $upgrader = ...; public $done_header = FALSE; public $done_footer = FALSE; public $result = [...]; public $options = [...]; protected $messages = [...] }; public $result = ['source' => 'D:/www/suarwoodtable/wp-content/upgrade/advanced-custom-fields-pro/advanced-custom-fields-pro/', 'source_files' => [...], 'destination' => 'D:\\www\\suarwoodtable/wp-content/plugins/advanced-custom-fields-pro/', 'destination_name' => 'advanced-custom-fields-pro', 'local_destination' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'remote_destination' => 'D:/www/suarwoodtable/wp-content/plugins/advanced-custom-fields-pro/', 'clear_destination' => TRUE]; public $update_count = 0; public $update_current = 0; private ${WP_Upgrader}temp_backups = [0 => [...]]; private ${WP_Upgrader}temp_restores = []; public $bulk = FALSE; public $new_plugin_data = [] }, $options = ['plugin' => 'advanced-custom-fields-pro/acf.php', 'type' => 'plugin', 'action' => 'update', 'temp_backup' => ['slug' => 'advanced-custom-fields-pro', 'src' => 'D:\\www\\suarwoodtable/wp-content/plugins', 'dir' => 'plugins']]) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 10:38:57 UTC]     Plugin 'advanced-custom-fields-pro' has been upgraded.
[02-Feb-2025 10:38:59 UTC]     Scraping home page...
[02-Feb-2025 10:39:49 UTC] Loopback request failed: cURL error 28: Operation timed out after 50009 milliseconds with 0 bytes received
[02-Feb-2025 10:39:49 UTC]     The update for 'advanced-custom-fields-pro' contained a fatal error. The previously installed version has been restored.
[02-Feb-2025 10:39:49 UTC]   Automatic plugin updates complete.
[02-Feb-2025 10:39:50 UTC] Automatic updates complete.
[02-Feb-2025 10:40:29 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Feb-2025 10:40:29 UTC] PHP Stack trace:
[02-Feb-2025 10:40:29 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin-ajax.php:0
[02-Feb-2025 10:40:29 UTC] PHP   2. do_action($hook_name = 'admin_init') D:\www\suarwoodtable\wp-admin\admin-ajax.php:45
[02-Feb-2025 10:40:29 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 10:40:29 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 10:40:29 UTC] PHP   5. init_bsf_core('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:02:38 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Feb-2025 11:02:38 UTC] PHP Stack trace:
[02-Feb-2025 11:02:38 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\index.php:0
[02-Feb-2025 11:02:38 UTC] PHP   2. require_once() D:\www\suarwoodtable\wp-admin\index.php:10
[02-Feb-2025 11:02:38 UTC] PHP   3. do_action($hook_name = 'admin_init') D:\www\suarwoodtable\wp-admin\admin.php:175
[02-Feb-2025 11:02:38 UTC] PHP   4. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:02:38 UTC] PHP   5. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:02:38 UTC] PHP   6. init_bsf_core('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:02:39 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Feb-2025 11:02:39 UTC] PHP Stack trace:
[02-Feb-2025 11:02:39 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\index.php:0
[02-Feb-2025 11:02:39 UTC] PHP   2. require_once() D:\www\suarwoodtable\wp-admin\index.php:137
[02-Feb-2025 11:02:39 UTC] PHP   3. do_action($hook_name = 'admin_enqueue_scripts', ...$arg = variadic('index.php')) D:\www\suarwoodtable\wp-admin\admin-header.php:118
[02-Feb-2025 11:02:39 UTC] PHP   4. WP_Hook->do_action($args = [0 => 'index.php']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:02:39 UTC] PHP   5. WP_Hook->apply_filters($value = '', $args = [0 => 'index.php']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:02:39 UTC] PHP   6. Ultimate_Admin_Area->bsf_admin_scripts_updater($hook = 'index.php') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:02:39 UTC] PHP   7. wp_add_inline_style($handle = 'wp-admin', $data = '\n\t\t\t\t<style>\n\t\t\t\t\t@font-face {\n\t\t\t\t\t\tfont-family: \'ultimate\';\n\t\t\t\t\t\tsrc:url(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fonts/ultimate.eot\');\n\t\t\t\t\t\tsrc:url(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fonts/ultimate.eot\') format(\'embedded-opentype\'),\n\t\t\t\t\t\t\turl(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fonts/ultimate.woff\') format(\'woff\'),\n\t\t\t\t\t\t\turl(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fo'...) D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\admin.php:250
[02-Feb-2025 11:02:39 UTC] PHP   8. _doing_it_wrong($function_name = 'wp_add_inline_style', $message = 'Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>.', $version = '3.7.0') D:\www\suarwoodtable\wp-includes\functions.wp-styles.php:91
[02-Feb-2025 11:02:39 UTC] PHP   9. wp_trigger_error($function_name = '', $message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = *uninitialized*) D:\www\suarwoodtable\wp-includes\functions.php:6054
[02-Feb-2025 11:02:39 UTC] PHP  10. trigger_error($message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = 1024) D:\www\suarwoodtable\wp-includes\functions.php:6114
[02-Feb-2025 11:02:40 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Feb-2025 11:02:40 UTC] PHP Stack trace:
[02-Feb-2025 11:02:40 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\index.php:0
[02-Feb-2025 11:02:40 UTC] PHP   2. require_once() D:\www\suarwoodtable\wp-admin\index.php:137
[02-Feb-2025 11:02:40 UTC] PHP   3. do_action($hook_name = 'admin_enqueue_scripts', ...$arg = variadic('index.php')) D:\www\suarwoodtable\wp-admin\admin-header.php:118
[02-Feb-2025 11:02:40 UTC] PHP   4. WP_Hook->do_action($args = [0 => 'index.php']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:02:40 UTC] PHP   5. WP_Hook->apply_filters($value = '', $args = [0 => 'index.php']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:02:40 UTC] PHP   6. Ultimate_VC_Addons_Carousel->custom_param_styles('index.php') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:02:40 UTC] PHP   7. wp_add_inline_style($handle = 'wp-admin', $data = '<style type="text/css">\n\t\t\t\t\t.items_to_show.vc_shortcode-param {\n\t\t\t\t\t\tbackground: #E6E6E6;\n\t\t\t\t\t\tpadding-bottom: 10px;\n\t\t\t\t\t}\n\t\t\t\t\t.items_to_show.ult_margin_bottom{\n\t\t\t\t\t\tmargin-bottom: 15px;\n\t\t\t\t\t}\n\t\t\t\t\t.items_to_show.ult_margin_top{\n\t\t\t\t\t\tmargin-top: 15px;\n\t\t\t\t\t}\n\t\t\t\t</style>') D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\modules\ultimate_carousel.php:49
[02-Feb-2025 11:02:40 UTC] PHP   8. _doing_it_wrong($function_name = 'wp_add_inline_style', $message = 'Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>.', $version = '3.7.0') D:\www\suarwoodtable\wp-includes\functions.wp-styles.php:91
[02-Feb-2025 11:02:40 UTC] PHP   9. wp_trigger_error($function_name = '', $message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = *uninitialized*) D:\www\suarwoodtable\wp-includes\functions.php:6054
[02-Feb-2025 11:02:40 UTC] PHP  10. trigger_error($message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = 1024) D:\www\suarwoodtable\wp-includes\functions.php:6114
[02-Feb-2025 11:02:45 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Feb-2025 11:02:45 UTC] PHP Stack trace:
[02-Feb-2025 11:02:45 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin-ajax.php:0
[02-Feb-2025 11:02:45 UTC] PHP   2. do_action($hook_name = 'admin_init') D:\www\suarwoodtable\wp-admin\admin-ajax.php:45
[02-Feb-2025 11:02:45 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:02:45 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:02:45 UTC] PHP   5. init_bsf_core('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:02:46 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Feb-2025 11:02:46 UTC] PHP Stack trace:
[02-Feb-2025 11:02:46 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin-ajax.php:0
[02-Feb-2025 11:02:46 UTC] PHP   2. do_action($hook_name = 'admin_init') D:\www\suarwoodtable\wp-admin\admin-ajax.php:45
[02-Feb-2025 11:02:46 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:02:46 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:02:46 UTC] PHP   5. init_bsf_core('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:02:51 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Feb-2025 11:02:51 UTC] PHP Stack trace:
[02-Feb-2025 11:02:51 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin.php:0
[02-Feb-2025 11:02:51 UTC] PHP   2. do_action($hook_name = 'admin_init') D:\www\suarwoodtable\wp-admin\admin.php:175
[02-Feb-2025 11:02:51 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:02:51 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:02:51 UTC] PHP   5. init_bsf_core('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:02:51 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Feb-2025 11:02:51 UTC] PHP Stack trace:
[02-Feb-2025 11:02:51 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin.php:0
[02-Feb-2025 11:02:51 UTC] PHP   2. require_once() D:\www\suarwoodtable\wp-admin\admin.php:239
[02-Feb-2025 11:02:51 UTC] PHP   3. do_action($hook_name = 'admin_enqueue_scripts', ...$arg = variadic('toplevel_page_wp-mail-smtp')) D:\www\suarwoodtable\wp-admin\admin-header.php:118
[02-Feb-2025 11:02:51 UTC] PHP   4. WP_Hook->do_action($args = [0 => 'toplevel_page_wp-mail-smtp']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:02:51 UTC] PHP   5. WP_Hook->apply_filters($value = '', $args = [0 => 'toplevel_page_wp-mail-smtp']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:02:51 UTC] PHP   6. Ultimate_Admin_Area->bsf_admin_scripts_updater($hook = 'toplevel_page_wp-mail-smtp') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:02:51 UTC] PHP   7. wp_add_inline_style($handle = 'wp-admin', $data = '\n\t\t\t\t<style>\n\t\t\t\t\t@font-face {\n\t\t\t\t\t\tfont-family: \'ultimate\';\n\t\t\t\t\t\tsrc:url(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fonts/ultimate.eot\');\n\t\t\t\t\t\tsrc:url(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fonts/ultimate.eot\') format(\'embedded-opentype\'),\n\t\t\t\t\t\t\turl(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fonts/ultimate.woff\') format(\'woff\'),\n\t\t\t\t\t\t\turl(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fo'...) D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\admin.php:250
[02-Feb-2025 11:02:51 UTC] PHP   8. _doing_it_wrong($function_name = 'wp_add_inline_style', $message = 'Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>.', $version = '3.7.0') D:\www\suarwoodtable\wp-includes\functions.wp-styles.php:91
[02-Feb-2025 11:02:51 UTC] PHP   9. wp_trigger_error($function_name = '', $message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = *uninitialized*) D:\www\suarwoodtable\wp-includes\functions.php:6054
[02-Feb-2025 11:02:51 UTC] PHP  10. trigger_error($message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = 1024) D:\www\suarwoodtable\wp-includes\functions.php:6114
[02-Feb-2025 11:02:51 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Feb-2025 11:02:51 UTC] PHP Stack trace:
[02-Feb-2025 11:02:51 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin.php:0
[02-Feb-2025 11:02:51 UTC] PHP   2. require_once() D:\www\suarwoodtable\wp-admin\admin.php:239
[02-Feb-2025 11:02:51 UTC] PHP   3. do_action($hook_name = 'admin_enqueue_scripts', ...$arg = variadic('toplevel_page_wp-mail-smtp')) D:\www\suarwoodtable\wp-admin\admin-header.php:118
[02-Feb-2025 11:02:51 UTC] PHP   4. WP_Hook->do_action($args = [0 => 'toplevel_page_wp-mail-smtp']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:02:51 UTC] PHP   5. WP_Hook->apply_filters($value = '', $args = [0 => 'toplevel_page_wp-mail-smtp']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:02:51 UTC] PHP   6. Ultimate_VC_Addons_Carousel->custom_param_styles('toplevel_page_wp-mail-smtp') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:02:51 UTC] PHP   7. wp_add_inline_style($handle = 'wp-admin', $data = '<style type="text/css">\n\t\t\t\t\t.items_to_show.vc_shortcode-param {\n\t\t\t\t\t\tbackground: #E6E6E6;\n\t\t\t\t\t\tpadding-bottom: 10px;\n\t\t\t\t\t}\n\t\t\t\t\t.items_to_show.ult_margin_bottom{\n\t\t\t\t\t\tmargin-bottom: 15px;\n\t\t\t\t\t}\n\t\t\t\t\t.items_to_show.ult_margin_top{\n\t\t\t\t\t\tmargin-top: 15px;\n\t\t\t\t\t}\n\t\t\t\t</style>') D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\modules\ultimate_carousel.php:49
[02-Feb-2025 11:02:51 UTC] PHP   8. _doing_it_wrong($function_name = 'wp_add_inline_style', $message = 'Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>.', $version = '3.7.0') D:\www\suarwoodtable\wp-includes\functions.wp-styles.php:91
[02-Feb-2025 11:02:51 UTC] PHP   9. wp_trigger_error($function_name = '', $message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = *uninitialized*) D:\www\suarwoodtable\wp-includes\functions.php:6054
[02-Feb-2025 11:02:51 UTC] PHP  10. trigger_error($message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = 1024) D:\www\suarwoodtable\wp-includes\functions.php:6114
[02-Feb-2025 11:03:30 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Feb-2025 11:03:30 UTC] PHP Stack trace:
[02-Feb-2025 11:03:30 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin.php:0
[02-Feb-2025 11:03:30 UTC] PHP   2. do_action($hook_name = 'admin_init') D:\www\suarwoodtable\wp-admin\admin.php:175
[02-Feb-2025 11:03:30 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:03:30 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:03:30 UTC] PHP   5. init_bsf_core('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:03:30 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Feb-2025 11:03:30 UTC] PHP Stack trace:
[02-Feb-2025 11:03:30 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin.php:0
[02-Feb-2025 11:03:30 UTC] PHP   2. require_once() D:\www\suarwoodtable\wp-admin\admin.php:239
[02-Feb-2025 11:03:30 UTC] PHP   3. do_action($hook_name = 'admin_enqueue_scripts', ...$arg = variadic('toplevel_page_wp-mail-smtp')) D:\www\suarwoodtable\wp-admin\admin-header.php:118
[02-Feb-2025 11:03:30 UTC] PHP   4. WP_Hook->do_action($args = [0 => 'toplevel_page_wp-mail-smtp']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:03:30 UTC] PHP   5. WP_Hook->apply_filters($value = '', $args = [0 => 'toplevel_page_wp-mail-smtp']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:03:30 UTC] PHP   6. Ultimate_Admin_Area->bsf_admin_scripts_updater($hook = 'toplevel_page_wp-mail-smtp') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:03:30 UTC] PHP   7. wp_add_inline_style($handle = 'wp-admin', $data = '\n\t\t\t\t<style>\n\t\t\t\t\t@font-face {\n\t\t\t\t\t\tfont-family: \'ultimate\';\n\t\t\t\t\t\tsrc:url(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fonts/ultimate.eot\');\n\t\t\t\t\t\tsrc:url(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fonts/ultimate.eot\') format(\'embedded-opentype\'),\n\t\t\t\t\t\t\turl(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fonts/ultimate.woff\') format(\'woff\'),\n\t\t\t\t\t\t\turl(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fo'...) D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\admin.php:250
[02-Feb-2025 11:03:30 UTC] PHP   8. _doing_it_wrong($function_name = 'wp_add_inline_style', $message = 'Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>.', $version = '3.7.0') D:\www\suarwoodtable\wp-includes\functions.wp-styles.php:91
[02-Feb-2025 11:03:30 UTC] PHP   9. wp_trigger_error($function_name = '', $message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = *uninitialized*) D:\www\suarwoodtable\wp-includes\functions.php:6054
[02-Feb-2025 11:03:30 UTC] PHP  10. trigger_error($message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = 1024) D:\www\suarwoodtable\wp-includes\functions.php:6114
[02-Feb-2025 11:03:30 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Feb-2025 11:03:30 UTC] PHP Stack trace:
[02-Feb-2025 11:03:30 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin.php:0
[02-Feb-2025 11:03:30 UTC] PHP   2. require_once() D:\www\suarwoodtable\wp-admin\admin.php:239
[02-Feb-2025 11:03:30 UTC] PHP   3. do_action($hook_name = 'admin_enqueue_scripts', ...$arg = variadic('toplevel_page_wp-mail-smtp')) D:\www\suarwoodtable\wp-admin\admin-header.php:118
[02-Feb-2025 11:03:30 UTC] PHP   4. WP_Hook->do_action($args = [0 => 'toplevel_page_wp-mail-smtp']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:03:30 UTC] PHP   5. WP_Hook->apply_filters($value = '', $args = [0 => 'toplevel_page_wp-mail-smtp']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:03:30 UTC] PHP   6. Ultimate_VC_Addons_Carousel->custom_param_styles('toplevel_page_wp-mail-smtp') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:03:30 UTC] PHP   7. wp_add_inline_style($handle = 'wp-admin', $data = '<style type="text/css">\n\t\t\t\t\t.items_to_show.vc_shortcode-param {\n\t\t\t\t\t\tbackground: #E6E6E6;\n\t\t\t\t\t\tpadding-bottom: 10px;\n\t\t\t\t\t}\n\t\t\t\t\t.items_to_show.ult_margin_bottom{\n\t\t\t\t\t\tmargin-bottom: 15px;\n\t\t\t\t\t}\n\t\t\t\t\t.items_to_show.ult_margin_top{\n\t\t\t\t\t\tmargin-top: 15px;\n\t\t\t\t\t}\n\t\t\t\t</style>') D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\modules\ultimate_carousel.php:49
[02-Feb-2025 11:03:30 UTC] PHP   8. _doing_it_wrong($function_name = 'wp_add_inline_style', $message = 'Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>.', $version = '3.7.0') D:\www\suarwoodtable\wp-includes\functions.wp-styles.php:91
[02-Feb-2025 11:03:30 UTC] PHP   9. wp_trigger_error($function_name = '', $message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = *uninitialized*) D:\www\suarwoodtable\wp-includes\functions.php:6054
[02-Feb-2025 11:03:30 UTC] PHP  10. trigger_error($message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = 1024) D:\www\suarwoodtable\wp-includes\functions.php:6114
[02-Feb-2025 11:03:51 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Feb-2025 11:03:51 UTC] PHP Stack trace:
[02-Feb-2025 11:03:51 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin.php:0
[02-Feb-2025 11:03:51 UTC] PHP   2. do_action($hook_name = 'admin_init') D:\www\suarwoodtable\wp-admin\admin.php:175
[02-Feb-2025 11:03:51 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:03:51 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:03:51 UTC] PHP   5. init_bsf_core('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:03:51 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Feb-2025 11:03:51 UTC] PHP Stack trace:
[02-Feb-2025 11:03:51 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin.php:0
[02-Feb-2025 11:03:51 UTC] PHP   2. require_once() D:\www\suarwoodtable\wp-admin\admin.php:239
[02-Feb-2025 11:03:51 UTC] PHP   3. do_action($hook_name = 'admin_enqueue_scripts', ...$arg = variadic('wp-mail-smtp_page_wp-mail-smtp-tools')) D:\www\suarwoodtable\wp-admin\admin-header.php:118
[02-Feb-2025 11:03:51 UTC] PHP   4. WP_Hook->do_action($args = [0 => 'wp-mail-smtp_page_wp-mail-smtp-tools']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:03:51 UTC] PHP   5. WP_Hook->apply_filters($value = '', $args = [0 => 'wp-mail-smtp_page_wp-mail-smtp-tools']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:03:51 UTC] PHP   6. Ultimate_Admin_Area->bsf_admin_scripts_updater($hook = 'wp-mail-smtp_page_wp-mail-smtp-tools') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:03:51 UTC] PHP   7. wp_add_inline_style($handle = 'wp-admin', $data = '\n\t\t\t\t<style>\n\t\t\t\t\t@font-face {\n\t\t\t\t\t\tfont-family: \'ultimate\';\n\t\t\t\t\t\tsrc:url(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fonts/ultimate.eot\');\n\t\t\t\t\t\tsrc:url(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fonts/ultimate.eot\') format(\'embedded-opentype\'),\n\t\t\t\t\t\t\turl(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fonts/ultimate.woff\') format(\'woff\'),\n\t\t\t\t\t\t\turl(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fo'...) D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\admin.php:250
[02-Feb-2025 11:03:51 UTC] PHP   8. _doing_it_wrong($function_name = 'wp_add_inline_style', $message = 'Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>.', $version = '3.7.0') D:\www\suarwoodtable\wp-includes\functions.wp-styles.php:91
[02-Feb-2025 11:03:51 UTC] PHP   9. wp_trigger_error($function_name = '', $message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = *uninitialized*) D:\www\suarwoodtable\wp-includes\functions.php:6054
[02-Feb-2025 11:03:51 UTC] PHP  10. trigger_error($message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = 1024) D:\www\suarwoodtable\wp-includes\functions.php:6114
[02-Feb-2025 11:03:51 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Feb-2025 11:03:51 UTC] PHP Stack trace:
[02-Feb-2025 11:03:51 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin.php:0
[02-Feb-2025 11:03:51 UTC] PHP   2. require_once() D:\www\suarwoodtable\wp-admin\admin.php:239
[02-Feb-2025 11:03:51 UTC] PHP   3. do_action($hook_name = 'admin_enqueue_scripts', ...$arg = variadic('wp-mail-smtp_page_wp-mail-smtp-tools')) D:\www\suarwoodtable\wp-admin\admin-header.php:118
[02-Feb-2025 11:03:51 UTC] PHP   4. WP_Hook->do_action($args = [0 => 'wp-mail-smtp_page_wp-mail-smtp-tools']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:03:51 UTC] PHP   5. WP_Hook->apply_filters($value = '', $args = [0 => 'wp-mail-smtp_page_wp-mail-smtp-tools']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:03:51 UTC] PHP   6. Ultimate_VC_Addons_Carousel->custom_param_styles('wp-mail-smtp_page_wp-mail-smtp-tools') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:03:51 UTC] PHP   7. wp_add_inline_style($handle = 'wp-admin', $data = '<style type="text/css">\n\t\t\t\t\t.items_to_show.vc_shortcode-param {\n\t\t\t\t\t\tbackground: #E6E6E6;\n\t\t\t\t\t\tpadding-bottom: 10px;\n\t\t\t\t\t}\n\t\t\t\t\t.items_to_show.ult_margin_bottom{\n\t\t\t\t\t\tmargin-bottom: 15px;\n\t\t\t\t\t}\n\t\t\t\t\t.items_to_show.ult_margin_top{\n\t\t\t\t\t\tmargin-top: 15px;\n\t\t\t\t\t}\n\t\t\t\t</style>') D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\modules\ultimate_carousel.php:49
[02-Feb-2025 11:03:51 UTC] PHP   8. _doing_it_wrong($function_name = 'wp_add_inline_style', $message = 'Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>.', $version = '3.7.0') D:\www\suarwoodtable\wp-includes\functions.wp-styles.php:91
[02-Feb-2025 11:03:51 UTC] PHP   9. wp_trigger_error($function_name = '', $message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = *uninitialized*) D:\www\suarwoodtable\wp-includes\functions.php:6054
[02-Feb-2025 11:03:51 UTC] PHP  10. trigger_error($message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = 1024) D:\www\suarwoodtable\wp-includes\functions.php:6114
[02-Feb-2025 11:03:58 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Feb-2025 11:03:58 UTC] PHP Stack trace:
[02-Feb-2025 11:03:58 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin.php:0
[02-Feb-2025 11:03:58 UTC] PHP   2. do_action($hook_name = 'admin_init') D:\www\suarwoodtable\wp-admin\admin.php:175
[02-Feb-2025 11:03:58 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:03:58 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:03:58 UTC] PHP   5. init_bsf_core('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:04:03 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Feb-2025 11:04:03 UTC] PHP Stack trace:
[02-Feb-2025 11:04:03 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin.php:0
[02-Feb-2025 11:04:03 UTC] PHP   2. require_once() D:\www\suarwoodtable\wp-admin\admin.php:239
[02-Feb-2025 11:04:03 UTC] PHP   3. do_action($hook_name = 'admin_enqueue_scripts', ...$arg = variadic('wp-mail-smtp_page_wp-mail-smtp-tools')) D:\www\suarwoodtable\wp-admin\admin-header.php:118
[02-Feb-2025 11:04:03 UTC] PHP   4. WP_Hook->do_action($args = [0 => 'wp-mail-smtp_page_wp-mail-smtp-tools']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:04:03 UTC] PHP   5. WP_Hook->apply_filters($value = '', $args = [0 => 'wp-mail-smtp_page_wp-mail-smtp-tools']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:04:03 UTC] PHP   6. Ultimate_Admin_Area->bsf_admin_scripts_updater($hook = 'wp-mail-smtp_page_wp-mail-smtp-tools') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:04:03 UTC] PHP   7. wp_add_inline_style($handle = 'wp-admin', $data = '\n\t\t\t\t<style>\n\t\t\t\t\t@font-face {\n\t\t\t\t\t\tfont-family: \'ultimate\';\n\t\t\t\t\t\tsrc:url(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fonts/ultimate.eot\');\n\t\t\t\t\t\tsrc:url(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fonts/ultimate.eot\') format(\'embedded-opentype\'),\n\t\t\t\t\t\t\turl(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fonts/ultimate.woff\') format(\'woff\'),\n\t\t\t\t\t\t\turl(\'http://suarwoodtable.local/wp-content/plugins/Ultimate_VC_Addons/admin/fo'...) D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\admin.php:250
[02-Feb-2025 11:04:03 UTC] PHP   8. _doing_it_wrong($function_name = 'wp_add_inline_style', $message = 'Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>.', $version = '3.7.0') D:\www\suarwoodtable\wp-includes\functions.wp-styles.php:91
[02-Feb-2025 11:04:03 UTC] PHP   9. wp_trigger_error($function_name = '', $message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = *uninitialized*) D:\www\suarwoodtable\wp-includes\functions.php:6054
[02-Feb-2025 11:04:03 UTC] PHP  10. trigger_error($message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = 1024) D:\www\suarwoodtable\wp-includes\functions.php:6114
[02-Feb-2025 11:04:03 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[02-Feb-2025 11:04:03 UTC] PHP Stack trace:
[02-Feb-2025 11:04:03 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin.php:0
[02-Feb-2025 11:04:03 UTC] PHP   2. require_once() D:\www\suarwoodtable\wp-admin\admin.php:239
[02-Feb-2025 11:04:03 UTC] PHP   3. do_action($hook_name = 'admin_enqueue_scripts', ...$arg = variadic('wp-mail-smtp_page_wp-mail-smtp-tools')) D:\www\suarwoodtable\wp-admin\admin-header.php:118
[02-Feb-2025 11:04:03 UTC] PHP   4. WP_Hook->do_action($args = [0 => 'wp-mail-smtp_page_wp-mail-smtp-tools']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:04:03 UTC] PHP   5. WP_Hook->apply_filters($value = '', $args = [0 => 'wp-mail-smtp_page_wp-mail-smtp-tools']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:04:03 UTC] PHP   6. Ultimate_VC_Addons_Carousel->custom_param_styles('wp-mail-smtp_page_wp-mail-smtp-tools') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:04:03 UTC] PHP   7. wp_add_inline_style($handle = 'wp-admin', $data = '<style type="text/css">\n\t\t\t\t\t.items_to_show.vc_shortcode-param {\n\t\t\t\t\t\tbackground: #E6E6E6;\n\t\t\t\t\t\tpadding-bottom: 10px;\n\t\t\t\t\t}\n\t\t\t\t\t.items_to_show.ult_margin_bottom{\n\t\t\t\t\t\tmargin-bottom: 15px;\n\t\t\t\t\t}\n\t\t\t\t\t.items_to_show.ult_margin_top{\n\t\t\t\t\t\tmargin-top: 15px;\n\t\t\t\t\t}\n\t\t\t\t</style>') D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\modules\ultimate_carousel.php:49
[02-Feb-2025 11:04:03 UTC] PHP   8. _doing_it_wrong($function_name = 'wp_add_inline_style', $message = 'Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>.', $version = '3.7.0') D:\www\suarwoodtable\wp-includes\functions.wp-styles.php:91
[02-Feb-2025 11:04:03 UTC] PHP   9. wp_trigger_error($function_name = '', $message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = *uninitialized*) D:\www\suarwoodtable\wp-includes\functions.php:6054
[02-Feb-2025 11:04:03 UTC] PHP  10. trigger_error($message = 'Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.)', $error_level = 1024) D:\www\suarwoodtable\wp-includes\functions.php:6114
[02-Feb-2025 11:05:05 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Feb-2025 11:05:05 UTC] PHP Stack trace:
[02-Feb-2025 11:05:05 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin-ajax.php:0
[02-Feb-2025 11:05:05 UTC] PHP   2. do_action($hook_name = 'admin_init') D:\www\suarwoodtable\wp-admin\admin-ajax.php:45
[02-Feb-2025 11:05:05 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:05:05 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:05:05 UTC] PHP   5. init_bsf_core('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:07:06 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Feb-2025 11:07:06 UTC] PHP Stack trace:
[02-Feb-2025 11:07:06 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin-ajax.php:0
[02-Feb-2025 11:07:06 UTC] PHP   2. do_action($hook_name = 'admin_init') D:\www\suarwoodtable\wp-admin\admin-ajax.php:45
[02-Feb-2025 11:07:06 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:07:06 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:07:06 UTC] PHP   5. init_bsf_core('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:09:07 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Feb-2025 11:09:07 UTC] PHP Stack trace:
[02-Feb-2025 11:09:07 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin-ajax.php:0
[02-Feb-2025 11:09:07 UTC] PHP   2. do_action($hook_name = 'admin_init') D:\www\suarwoodtable\wp-admin\admin-ajax.php:45
[02-Feb-2025 11:09:07 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:09:07 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:09:07 UTC] PHP   5. init_bsf_core('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:11:08 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Feb-2025 11:11:08 UTC] PHP Stack trace:
[02-Feb-2025 11:11:08 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin-ajax.php:0
[02-Feb-2025 11:11:08 UTC] PHP   2. do_action($hook_name = 'admin_init') D:\www\suarwoodtable\wp-admin\admin-ajax.php:45
[02-Feb-2025 11:11:08 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:11:08 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:11:08 UTC] PHP   5. init_bsf_core('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:12:08 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Feb-2025 11:12:08 UTC] PHP Stack trace:
[02-Feb-2025 11:12:08 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin-ajax.php:0
[02-Feb-2025 11:12:08 UTC] PHP   2. do_action($hook_name = 'admin_init') D:\www\suarwoodtable\wp-admin\admin-ajax.php:45
[02-Feb-2025 11:12:08 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:12:08 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:12:08 UTC] PHP   5. init_bsf_core('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[02-Feb-2025 11:13:09 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[02-Feb-2025 11:13:09 UTC] PHP Stack trace:
[02-Feb-2025 11:13:09 UTC] PHP   1. {main}() D:\www\suarwoodtable\wp-admin\admin-ajax.php:0
[02-Feb-2025 11:13:09 UTC] PHP   2. do_action($hook_name = 'admin_init') D:\www\suarwoodtable\wp-admin\admin-ajax.php:45
[02-Feb-2025 11:13:09 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\www\suarwoodtable\wp-includes\plugin.php:517
[02-Feb-2025 11:13:09 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\www\suarwoodtable\wp-includes\class-wp-hook.php:348
[02-Feb-2025 11:13:09 UTC] PHP   5. init_bsf_core('') D:\www\suarwoodtable\wp-includes\class-wp-hook.php:324
[27-Jun-2025 20:27:44 UTC] Automatic updates starting...
[27-Jun-2025 20:27:52 UTC]   Automatic plugin updates starting...
[27-Jun-2025 20:27:52 UTC]     Upgrading plugin 'all-in-one-wp-migration'...
[27-Jun-2025 20:27:59 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[27-Jun-2025 20:27:59 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[27-Jun-2025 20:27:59 UTC]     Plugin 'all-in-one-wp-migration' has been upgraded.
[27-Jun-2025 20:28:01 UTC]     Scraping home page...
[27-Jun-2025 20:28:52 UTC] Loopback request failed: cURL error 28: Operation timed out after 50015 milliseconds with 0 bytes received
[27-Jun-2025 20:28:52 UTC]     The update for 'all-in-one-wp-migration' contained a fatal error. The previously installed version has been restored.
[27-Jun-2025 20:28:52 UTC]     Upgrading plugin 'contact-form-7'...
[27-Jun-2025 20:28:55 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[27-Jun-2025 20:28:55 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[27-Jun-2025 20:28:55 UTC]     Plugin 'contact-form-7' has been upgraded.
[27-Jun-2025 20:28:57 UTC]     Scraping home page...
[27-Jun-2025 20:29:47 UTC] Loopback request failed: cURL error 28: Operation timed out after 50001 milliseconds with 0 bytes received
[27-Jun-2025 20:29:47 UTC]     The update for 'contact-form-7' contained a fatal error. The previously installed version has been restored.
[27-Jun-2025 20:29:47 UTC]     Upgrading plugin 'filester'...
[27-Jun-2025 20:29:53 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[27-Jun-2025 20:29:53 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[27-Jun-2025 20:29:53 UTC]     Plugin 'filester' has been upgraded.
[27-Jun-2025 20:29:53 UTC]     'filester/ninja-file-manager.php' is inactive and will not be checked for fatal errors.
[27-Jun-2025 20:29:53 UTC]     Upgrading plugin 'ga-google-analytics'...
[27-Jun-2025 20:29:56 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[27-Jun-2025 20:29:56 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[27-Jun-2025 20:29:56 UTC]     Plugin 'ga-google-analytics' has been upgraded.
[27-Jun-2025 20:29:56 UTC]     'ga-google-analytics/ga-google-analytics.php' is inactive and will not be checked for fatal errors.
[27-Jun-2025 20:29:56 UTC]     Upgrading plugin 'meta-tag-manager'...
[27-Jun-2025 20:29:59 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[27-Jun-2025 20:29:59 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[27-Jun-2025 20:29:59 UTC]     Plugin 'meta-tag-manager' has been upgraded.
[27-Jun-2025 20:30:01 UTC]     Scraping home page...
[27-Jun-2025 20:30:51 UTC] Loopback request failed: cURL error 28: Operation timed out after 50015 milliseconds with 0 bytes received
[27-Jun-2025 20:30:51 UTC]     The update for 'meta-tag-manager' contained a fatal error. The previously installed version has been restored.
[27-Jun-2025 20:30:51 UTC]     Upgrading plugin 'nginx-helper'...
[27-Jun-2025 20:30:54 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[27-Jun-2025 20:30:54 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[27-Jun-2025 20:30:54 UTC]     Plugin 'nginx-helper' has been upgraded.
[27-Jun-2025 20:30:54 UTC]     'nginx-helper/nginx-helper.php' is inactive and will not be checked for fatal errors.
[27-Jun-2025 20:30:54 UTC]     Upgrading plugin 'post-duplicator'...
[27-Jun-2025 20:30:59 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[27-Jun-2025 20:30:59 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[27-Jun-2025 20:30:59 UTC]     Plugin 'post-duplicator' has been upgraded.
[27-Jun-2025 20:31:01 UTC]     Scraping home page...
[27-Jun-2025 20:31:51 UTC] Loopback request failed: cURL error 28: Operation timed out after 50006 milliseconds with 0 bytes received
[27-Jun-2025 20:31:51 UTC]     The update for 'post-duplicator' contained a fatal error. The previously installed version has been restored.
[27-Jun-2025 20:31:51 UTC]     Upgrading plugin 'really-simple-captcha'...
[27-Jun-2025 20:31:58 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[27-Jun-2025 20:31:58 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[27-Jun-2025 20:31:58 UTC]     Plugin 'really-simple-captcha' has been upgraded.
[27-Jun-2025 20:32:00 UTC]     Scraping home page...
[27-Jun-2025 20:32:50 UTC] Loopback request failed: cURL error 28: Operation timed out after 50002 milliseconds with 0 bytes received
[27-Jun-2025 20:32:50 UTC]     The update for 'really-simple-captcha' contained a fatal error. The previously installed version has been restored.
[27-Jun-2025 20:32:50 UTC]     Upgrading plugin 'shortpixel-image-optimiser'...
[27-Jun-2025 20:32:58 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[27-Jun-2025 20:32:58 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[27-Jun-2025 20:32:58 UTC]     Plugin 'shortpixel-image-optimiser' has been upgraded.
[27-Jun-2025 20:32:58 UTC]     'shortpixel-image-optimiser/wp-shortpixel.php' is inactive and will not be checked for fatal errors.
[27-Jun-2025 20:32:58 UTC]     Upgrading plugin 'wp-mail-smtp'...
[27-Jun-2025 20:33:08 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[27-Jun-2025 20:33:08 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[27-Jun-2025 20:38:08 UTC] PHP Fatal error:  Maximum execution time of 300 seconds exceeded in D:\www\suarwoodtable\wp-includes\Requests\src\Transport\Curl.php on line 205
[27-Jun-2025 20:38:22 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:41:32 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:41:33 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[27-Jun-2025 20:41:34 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[27-Jun-2025 20:41:34 UTC] PHP Warning:  Undefined variable $link in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\templates\params\notice\notice.php on line 16
[27-Jun-2025 20:41:38 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:41:40 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:43:09 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:43:13 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:43:20 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[27-Jun-2025 20:43:20 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[27-Jun-2025 20:43:20 UTC] PHP Warning:  Undefined variable $link in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\templates\params\notice\notice.php on line 16
[27-Jun-2025 20:48:03 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:48:08 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[27-Jun-2025 20:48:08 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[27-Jun-2025 20:48:08 UTC] PHP Warning:  Undefined variable $link in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\templates\params\notice\notice.php on line 16
[27-Jun-2025 20:48:11 UTC] PHP Warning:  get_core_checksums(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6114
[27-Jun-2025 20:50:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>acf</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:50:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:50:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>acf</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:50:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:50:43 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:50:43 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:50:43 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:50:43 UTC] PHP Warning:  Undefined variable $link in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\templates\params\notice\notice.php on line 16
[27-Jun-2025 20:51:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>acf</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:51:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:51:45 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:51:45 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>acf</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:51:45 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:52:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>acf</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:52:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:52:45 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:52:45 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>acf</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:52:45 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:53:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>acf</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:53:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:53:39 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:53:42 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:53:42 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:53:42 UTC] PHP Warning:  Undefined variable $link in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\templates\params\notice\notice.php on line 16
[27-Jun-2025 20:53:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>acf</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:53:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:53:46 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>acf</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:53:46 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:53:46 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:54:06 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:54:06 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:54:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:54:17 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:54:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:54:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:54:24 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:54:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:54:31 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:54:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:54:38 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:54:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:54:47 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:54:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:54:47 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:55:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:55:43 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:55:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:56:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:56:43 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:56:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:56:56 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:56:56 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:56:56 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:56:56 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:56:56 UTC] PHP Warning:  Undefined variable $link in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\templates\params\notice\notice.php on line 16
[27-Jun-2025 20:57:00 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:57:00 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:57:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:57:21 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:57:21 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:57:21 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:57:21 UTC] PHP Warning:  Undefined variable $link in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\templates\params\notice\notice.php on line 16
[27-Jun-2025 20:57:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:57:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:57:35 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:57:35 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:57:36 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:57:36 UTC] PHP Warning:  Undefined variable $link in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\templates\params\notice\notice.php on line 16
[27-Jun-2025 20:57:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:57:36 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:57:46 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:57:46 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:57:56 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:57:56 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:58:06 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:58:06 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:58:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:58:17 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 20:58:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:58:27 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 20:58:27 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:00:28 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:00:29 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:00:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:02:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:02:30 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:02:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:03:57 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:03:58 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:03:58 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:04:07 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:04:07 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:04:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:04:18 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:04:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:04:27 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:04:27 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:04:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:04:37 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:04:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:04:47 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:04:57 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:04:57 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:05:07 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:05:07 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:05:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:05:19 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:05:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:07:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:07:20 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:07:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:09:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:09:21 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:09:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:11:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:11:22 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:11:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:13:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[27-Jun-2025 21:13:23 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[27-Jun-2025 21:13:23 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:47:23 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:47:26 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 03:47:26 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:47:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:47:34 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 03:49:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:49:36 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 03:49:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:49:38 UTC] Automatic updates starting...
[28-Jun-2025 03:49:46 UTC]   Automatic plugin updates starting...
[28-Jun-2025 03:49:46 UTC]     Upgrading plugin 'filester'...
[28-Jun-2025 03:49:51 UTC] PHP Warning:  Undefined array key "plugins" in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 130
[28-Jun-2025 03:49:51 UTC] PHP Warning:  foreach() argument must be of type array|object, null given in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\autoload\vc-pages\welcome-screen.php on line 131
[28-Jun-2025 03:49:51 UTC]     Plugin 'filester' has been upgraded.
[28-Jun-2025 03:49:51 UTC]     'filester/ninja-file-manager.php' is inactive and will not be checked for fatal errors.
[28-Jun-2025 03:49:51 UTC]   Automatic plugin updates complete.
[28-Jun-2025 03:49:55 UTC]   Automatic theme updates starting...
[28-Jun-2025 03:49:55 UTC]     Upgrading theme 'twentytwentyfive'...
[28-Jun-2025 03:50:05 UTC]     Theme 'twentytwentyfive' has been upgraded.
[28-Jun-2025 03:50:05 UTC]   Automatic theme updates complete.
[28-Jun-2025 03:50:05 UTC] Automatic updates complete.
[28-Jun-2025 03:50:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:50:21 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 03:50:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:51:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:51:48 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 03:51:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:52:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:52:22 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 03:52:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:52:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:52:31 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 03:52:57 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:52:57 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 03:53:07 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:53:07 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 03:55:08 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:55:09 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 03:55:09 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:57:09 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:57:11 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 03:57:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:58:52 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 03:58:53 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 03:58:53 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 04:00:52 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 04:00:53 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 04:00:53 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 04:02:53 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 04:02:54 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 04:02:54 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 04:04:54 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 04:04:55 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 04:04:55 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 04:06:55 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 04:06:56 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 04:06:56 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 04:08:56 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 04:08:57 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 04:08:57 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:26:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:26:43 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 05:26:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:26:58 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:26:59 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 05:27:09 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:27:09 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 05:27:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:27:23 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 05:27:23 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:27:32 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:27:32 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 05:29:33 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:29:34 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 05:29:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:31:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:31:35 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 05:31:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:33:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:33:36 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 05:33:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:35:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:35:37 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 05:35:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:37:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 05:37:38 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 05:37:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 06:44:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 06:44:18 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 06:44:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 06:44:27 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 06:44:27 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 06:44:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 06:44:38 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 06:46:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 06:46:40 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 06:46:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 06:48:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 06:48:41 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 06:48:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 06:50:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 06:50:42 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 06:50:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 06:52:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 06:52:43 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 06:52:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 06:54:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 06:54:44 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 06:54:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 07:57:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 07:57:20 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 07:57:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 07:59:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 07:59:21 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 12:49:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 12:50:11 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 12:50:11 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 12:50:12 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 12:50:12 UTC] PHP Warning:  Undefined variable $link in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\templates\params\notice\notice.php on line 16
[28-Jun-2025 12:50:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 12:51:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 12:51:16 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 12:51:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 12:53:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 12:53:17 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 12:53:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 12:55:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 12:55:18 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 12:55:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 12:57:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 12:57:19 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 12:57:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 12:59:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 12:59:20 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 12:59:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:24:09 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:24:10 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 13:24:10 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:24:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:24:34 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 13:24:39 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:24:39 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:24:39 UTC] PHP Warning:  Undefined variable $link in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\templates\params\notice\notice.php on line 16
[28-Jun-2025 13:25:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:25:41 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 13:25:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:26:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:26:41 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 13:26:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:27:10 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:27:11 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 13:27:11 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:27:11 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:27:12 UTC] PHP Warning:  Undefined variable $link in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\templates\params\notice\notice.php on line 16
[28-Jun-2025 13:27:12 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:28:12 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:28:13 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 13:28:13 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:28:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:28:15 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 13:28:15 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:28:15 UTC] PHP Notice:  Function wp_add_inline_style was called <strong>incorrectly</strong>. Do not pass <code>&lt;style&gt;</code> tags to <code>wp_add_inline_style()</code>. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 3.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:28:16 UTC] PHP Warning:  Undefined variable $link in D:\www\suarwoodtable\wp-content\plugins\js_composer\include\templates\params\notice\notice.php on line 16
[28-Jun-2025 13:29:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:29:17 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 13:29:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:31:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:31:17 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 13:31:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:33:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
[28-Jun-2025 13:33:18 UTC] PHP Warning:  Undefined array key "template" in D:\www\suarwoodtable\wp-content\plugins\Ultimate_VC_Addons\admin\bsf-core\index.php on line 327
[28-Jun-2025 13:33:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>js_composer</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\www\suarwoodtable\wp-includes\functions.php on line 6121
