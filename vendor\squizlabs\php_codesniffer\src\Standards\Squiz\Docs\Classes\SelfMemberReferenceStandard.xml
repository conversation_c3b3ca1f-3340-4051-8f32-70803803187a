<documentation title="Self Member Reference">
    <standard>
    <![CDATA[
    The self keyword must be lowercase.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Lowercase self used.">
        <![CDATA[
class Bar {
    public function baz() {
        <em>self</em>::foo();
    }
}
        ]]>
        </code>
        <code title="Invalid: Uppercase self used.">
        <![CDATA[
class Bar {
    public function baz() {
        <em>SELF</em>::foo();
    }
}
        ]]>
        </code>
    </code_comparison>
    <standard>
    <![CDATA[
    There must be no spaces before or after the double colon operator.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: No spaces around the double colon operator.">
        <![CDATA[
class Bar {
    public function baz() {
        self<em></em>::<em></em>foo();
    }
}
        ]]>
        </code>
        <code title="Invalid: Spaces around double colon operator.">
        <![CDATA[
class Bar {
    public function baz() {
        self<em> </em>::<em> </em>foo();
    }
}
        ]]>
        </code>
    </code_comparison>
    <standard>
    <![CDATA[
    The self keyword must be used instead of the current class name.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Self used as reference.">
        <![CDATA[
class Foo {
    public function bar() {}

    public function baz() {
        <em>self</em>::bar();
    }
}
        ]]>
        </code>
        <code title="Invalid: Local class name used as reference.">
        <![CDATA[
class <em>Foo</em> {
    public function bar() {}

    public function baz() {
        <em>Foo</em>::bar();
    }
}
        ]]>
        </code>
    </code_comparison>
</documentation>
